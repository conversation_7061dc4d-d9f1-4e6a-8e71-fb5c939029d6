{"name": "typescript-orm-benchmark", "module": "index.ts", "type": "module", "devDependencies": {"bun-types": "latest"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"@faker-js/faker": "^9.8.0", "@mikro-orm/core": "^6.4.16", "@mikro-orm/mysql": "^6.4.16", "@prisma/client": "^6.9.0", "drizzle-orm": "^0.44.2", "knex": "^3.1.0", "kysely": "^0.28.2", "mariadb": "^3.4.2", "mitata": "^1.0.34", "mysql2": "^3.14.1", "prisma": "^6.9.0", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.7", "ts-node": "^10.9.2", "typeorm": "^0.3.24"}}