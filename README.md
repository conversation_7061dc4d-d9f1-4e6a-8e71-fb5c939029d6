# TypeScript (and JavaScript) ORMs Benchmark

Object-Relational Mapping (ORM) is a powerful technique that allows you to interact with databases using the same language you’re using for your application. This project aims to provide an objective assessment of the performance of popular Node.js ORMs.

## Results

Check [results](./results)

## List of ORM

### MySQL

- [DrizzleORM](./src/mysql/drizzle.ts)
- [KnexJS](./src/mysql/knex.ts)
- [<PERSON>ys<PERSON>](./src/mysql/kysely.ts)
- [Mariadb](./src/mysql/mariadb.ts)
- [MikroORM](./src/mysql/mikro.ts)
- [MySQL2](./src/mysql/mysql2.ts)
- [Prisma](./src/mysql/prisma.ts)
- [Sequelize](./src/mysql/sequelize.ts)
- [TypeORM](./src/mysql/typeorm.ts)


## License
This project is licensed under the MIT License. Feel free to explore, contribute, and share your insights!
