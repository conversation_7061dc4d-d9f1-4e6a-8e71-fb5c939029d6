# Database Configuration

This directory contains database configuration files for the TypeScript ORM benchmark project.

## Files

- `db.example.ts` - Template configuration file with placeholder values
- `db.ts` - Actual configuration file with real database credentials (not tracked in git)

## Setup Instructions

1. **Copy the example file:**
   ```bash
   cp config/db.example.ts config/db.ts
   ```

2. **Update the configuration:**
   Edit `config/db.ts` with your actual database credentials.

3. **Add to .gitignore:**
   Make sure your `.gitignore` file includes:
   ```
   config/db.ts
   ```

## Usage

### Import the configuration:
```typescript
import { databases, getDatabaseConfig, createConnectionString } from './config/db';
```

### Get a specific database config:
```typescript
const config = getDatabaseConfig('accoc_staging');
```

### Create connection strings:
```typescript
const connectionString = createConnectionString('accoc_staging');
// Returns: mysql://root:admin@localhost:65107/accoc_staging
```

### Get ORM-specific configurations:
```typescript
import { getTypeORMConfig, getMikroORMConfig, getPrismaConfig } from './config/db';

// TypeORM
const typeormConfig = getTypeORMConfig('accoc_staging');

// MikroORM
const mikroConfig = getMikroORMConfig('accoc_staging');

// Prisma
const prismaConfig = getPrismaConfig('accoc_staging');
```

### Environment-based configuration:
```typescript
import { getCurrentDatabaseConfig } from './config/db';

// Uses DB_NAME environment variable or defaults to 'accoc_staging'
const config = getCurrentDatabaseConfig();
```

## Available Databases

- `devapi` - Development API database
- `staging3_dev` - Staging 3 development database  
- `accoc_staging` - ACCOC staging database

## Security Notes

- **Never commit `db.ts` to version control**
- Use environment variables for production deployments
- Keep database credentials secure and rotate them regularly
- Use different credentials for different environments

## Environment Variables

You can override database selection using:
```bash
export DB_NAME=staging3_dev
```

Or set it when running commands:
```bash
DB_NAME=devapi npm run benchmark
```
