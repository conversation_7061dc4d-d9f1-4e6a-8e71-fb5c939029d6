// Database configuration for different environments
// This file contains actual database credentials - DO NOT commit to version control

export interface DatabaseConfig {
  database: string;
  userPattern: string;
  port: number;
  host: string;
  user: string;
  password: string;
}

export const databases: Record<string, DatabaseConfig> = {
  devapi: {
    database: 'devapi',
    userPattern: 'devapi',
    port: 65107,
    host: 'localhost',
    user: 'root',
    password: 'admin',
  },
  staging3_dev: {
    database: 'staging3',
    userPattern: 'staging3_dev',
    port: 65107,
    host: 'localhost',
    user: 'root',
    password: 'admin',
  },
  accoc_staging: {
    database: 'accoc_staging',
    userPattern: 'accoc_staging',
    port: 65107,
    host: 'localhost',
    user: 'root',
    password: 'admin',
  }
};

// Helper function to get database config by name
export function getDatabaseConfig(name: keyof typeof databases): DatabaseConfig {
  const config = databases[name];
  if (!config) {
    throw new Error(`Database configuration '${name}' not found`);
  }
  return config;
}

// Helper function to create connection string for different ORMs
export function createConnectionString(dbName: keyof typeof databases): string {
  const config = getDatabaseConfig(dbName);
  return `mysql://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`;
}

// Helper function to get TypeORM configuration
export function getTypeORMConfig(dbName: keyof typeof databases) {
  const config = getDatabaseConfig(dbName);
  return {
    type: 'mysql' as const,
    host: config.host,
    port: config.port,
    username: config.user,
    password: config.password,
    database: config.database,
    synchronize: false,
    logging: false
  };
}

// Helper function to get MikroORM configuration
export function getMikroORMConfig(dbName: keyof typeof databases) {
  const config = getDatabaseConfig(dbName);
  return {
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password,
    dbName: config.database,
    debug: false,
    allowGlobalContext: true
  };
}

// Helper function to get Prisma configuration
export function getPrismaConfig(dbName: keyof typeof databases) {
  const connectionString = createConnectionString(dbName);
  return {
    log: ['error'] as const,
    datasources: {
      db: {
        url: connectionString
      }
    }
  };
}

// Default database for development
export const DEFAULT_DATABASE = 'accoc_staging';

// Get current database from environment or use default
export function getCurrentDatabaseConfig(): DatabaseConfig {
  const dbName = (process.env.DB_NAME as keyof typeof databases) || DEFAULT_DATABASE;
  return getDatabaseConfig(dbName);
}
