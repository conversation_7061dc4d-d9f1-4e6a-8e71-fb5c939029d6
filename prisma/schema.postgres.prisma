generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model users {
  id         Int       @id @default(autoincrement())
  username   String    @db.VarChar(255)
  email      String    @db.VarChar(255)
  password   String    @db.VarChar(255)
  name       String    @db.VarChar(255)
  created_at DateTime  @default(now()) @db.Timestamptz(6)
  updated_at DateTime  @default(now()) @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  @@index([email], map: "users_email_index")
  @@index([username], map: "users_username_index")
}
