generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model users {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  username   String    @db.VarChar(255)
  email      String    @db.VarChar(255)
  password   String    @db.VarChar(255)
  name       String    @db.VarChar(255)
  created_at DateTime  @default(now()) @db.DateTime(0)
  updated_at DateTime  @default(now()) @db.DateTime(0)
  deleted_at DateTime? @db.DateTime(0)

  @@index([email], map: "users_email_index")
  @@index([username], map: "users_username_index")
}
