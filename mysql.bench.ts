import { run, bench, group } from 'mitata';
import { DataSource } from 'typeorm';
import { PrismaClient } from '@prisma/client';
import { MikroORM } from '@mikro-orm/core';
import { MySqlDriver } from '@mikro-orm/mysql';

// Import our benchmark services
import { StageBenchmark } from './src/typeorm';
import { PrismaStageBenchmark } from './src/prisma';
import { MikroORMStageBenchmark } from './src/mikroorm';

// Test parameters for sp_create_stage
const testParams = {
  name: `Bench_Stage_${Date.now()}`,
  stage_work_level_id: 1,
  shape: 'Square',
  stage_code_id: 1,
  metric_work_item_column_id: 1,
  locked: 0,
  bim: 1,
  fab: 1,
  field: 0,
  user_id: 1,
  nestable: 1,
  rejectable: 1,
  groupable: 1
};

// Initialize database connections
let typeormDataSource: DataSource;
let prismaClient: PrismaClient;
let mikroOrm: MikroORM;

// Setup function
async function setup() {
  // TypeORM setup
  typeormDataSource = new DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'accoc_staging',
    entities: ['./src/typeorm/*.entity.ts'],
    synchronize: false,
    logging: false
  });
  await typeormDataSource.initialize();

  // Prisma setup
  prismaClient = new PrismaClient({
    log: ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL || 'mysql://root:@localhost:3306/accoc_staging'
      }
    }
  });
  await prismaClient.$connect();

  // MikroORM setup
  mikroOrm = await MikroORM.init<MySqlDriver>({
    driver: MySqlDriver,
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    dbName: process.env.DB_NAME || 'accoc_staging',
    entities: ['./src/mysql/mikroorm/entities/*.entity.ts'],
    debug: false,
    allowGlobalContext: true
  });

  // Drizzle setup would go here when ready
  // const connection = await mysql.createConnection({...});
  // drizzleDb = drizzle(connection);
}

// Cleanup function
async function cleanup() {
  if (typeormDataSource?.isInitialized) {
    await typeormDataSource.destroy();
  }
  if (prismaClient) {
    await prismaClient.$disconnect();
  }
  if (mikroOrm) {
    await mikroOrm.close();
  }
  // Drizzle cleanup would be handled by closing the mysql connection
}

// Benchmark groups
group('MySQL sp_create_stage - Stored Procedures', async () => {
  await setup();

  const typeormBench = new StageBenchmark(typeormDataSource);
  const prismaBench = new PrismaStageBenchmark(prismaClient);
  const mikroBench = new MikroORMStageBenchmark(mikroOrm.em);

  bench('TypeORM Stored Procedure', async () => {
    const params = { ...testParams, name: `TypeORM_SP_${Date.now()}_${Math.random()}` };
    const result = await typeormBench.runSingleBenchmark(params);
    return result.storedProcedure;
  });

  bench('Prisma Stored Procedure', async () => {
    const params = { ...testParams, name: `Prisma_SP_${Date.now()}_${Math.random()}` };
    const result = await prismaBench.runSingleBenchmark(params);
    return result.storedProcedure;
  });

  bench('MikroORM Stored Procedure', async () => {
    const params = { ...testParams, name: `Mikro_SP_${Date.now()}_${Math.random()}` };
    const result = await mikroBench.runSingleBenchmark(params);
    return result.storedProcedure;
  });
});

group('MySQL sp_create_stage - ORM Entities', async () => {
  const typeormBench = new StageBenchmark(typeormDataSource);
  const prismaBench = new PrismaStageBenchmark(prismaClient);
  const mikroBench = new MikroORMStageBenchmark(mikroOrm.em);

  bench('TypeORM Entities', async () => {
    const params = { ...testParams, name: `TypeORM_ENT_${Date.now()}_${Math.random()}` };
    const result = await typeormBench.runSingleBenchmark(params);
    return result.entities;
  });

  bench('Prisma Client', async () => {
    const params = { ...testParams, name: `Prisma_ENT_${Date.now()}_${Math.random()}` };
    const result = await prismaBench.runSingleBenchmark(params);
    return result.prismaClient;
  });

  bench('MikroORM Entities', async () => {
    const params = { ...testParams, name: `Mikro_ENT_${Date.now()}_${Math.random()}` };
    const result = await mikroBench.runSingleBenchmark(params);
    return result.entities;
  });
});

group('MySQL sp_create_stage - Query Builders', async () => {
  const typeormBench = new StageBenchmark(typeormDataSource);
  const prismaBench = new PrismaStageBenchmark(prismaClient);
  const mikroBench = new MikroORMStageBenchmark(mikroOrm.em);

  bench('TypeORM Query Builder', async () => {
    const params = { ...testParams, name: `TypeORM_QB_${Date.now()}_${Math.random()}` };
    const result = await typeormBench.runSingleBenchmark(params);
    return result.queryBuilder;
  });

  bench('Prisma Advanced Queries', async () => {
    const params = { ...testParams, name: `Prisma_QB_${Date.now()}_${Math.random()}` };
    const result = await prismaBench.runSingleBenchmark(params);
    return result.advancedQueries;
  });

  bench('MikroORM Query Builder', async () => {
    const params = { ...testParams, name: `Mikro_QB_${Date.now()}_${Math.random()}` };
    const result = await mikroBench.runSingleBenchmark(params);
    return result.queryBuilder;
  });
});

await run({
  colors: true
});

await cleanup();
process.exit(0);
