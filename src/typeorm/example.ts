import { DataSource } from 'typeorm';
import { StageBenchmark } from './StageBenchmark';
import { 
  User, 
  Stage, 
  StageWorkLevel, 
  StageCode, 
  WorkItemColumn, 
  StageHistory 
} from './index';

// Example usage of the Stage benchmark
async function runStageBenchmarkExample() {
  // Create TypeORM DataSource (you'll need to configure this with your database settings)
  const dataSource = new DataSource({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'your_username',
    password: 'your_password',
    database: 'accoc_staging',
    entities: [User, Stage, StageWorkLevel, StageCode, WorkItemColumn, StageHistory],
    synchronize: false, // Don't auto-sync in production
    logging: false // Set to true for SQL query logging
  });

  try {
    // Initialize the connection
    await dataSource.initialize();
    console.log('Database connection established');

    // Create benchmark instance
    const benchmark = new StageBenchmark(dataSource);

    // Run a single benchmark test
    console.log('\n=== Running Single Benchmark Test ===');
    const singleResult = await benchmark.runSingleBenchmark({
      name: `Test_Stage_${Date.now()}`,
      stage_work_level_id: 1,
      shape: 'Square',
      stage_code_id: 1,
      metric_work_item_column_id: 1,
      locked: 0,
      bim: 1,
      fab: 1,
      field: 0,
      user_id: 1,
      nestable: 1,
      rejectable: 1,
      groupable: 1
    });

    console.log('Single test results:');
    console.log(`Stored Procedure: ${singleResult.storedProcedure.executionTime.toFixed(2)}ms - ${singleResult.storedProcedure.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`TypeORM Entities: ${singleResult.entities.executionTime.toFixed(2)}ms - ${singleResult.entities.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`Query Builder: ${singleResult.queryBuilder.executionTime.toFixed(2)}ms - ${singleResult.queryBuilder.success ? 'SUCCESS' : 'FAILED'}`);

    // Run full benchmark suite
    console.log('\n=== Running Full Benchmark Suite ===');
    const results = await benchmark.runBenchmarkSuite(10); // 10 iterations

    // Print formatted results
    benchmark.printResults(results);

    // You can also access raw data for further analysis
    console.log('\n=== Raw Data Sample ===');
    console.log('First 3 stored procedure results:');
    results.storedProcedure.slice(0, 3).forEach((result, index) => {
      console.log(`  ${index + 1}: ${result.executionTime.toFixed(2)}ms - ${result.success ? 'SUCCESS' : 'FAILED'}`);
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
    });

  } catch (error) {
    console.error('Benchmark failed:', error);
  } finally {
    // Clean up
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('\nDatabase connection closed');
    }
  }
}

// Export for use in other files
export { runStageBenchmarkExample };

// Uncomment to run directly
// runStageBenchmarkExample().catch(console.error);
