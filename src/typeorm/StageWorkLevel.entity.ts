import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('stage_work_levels')
@Index('unq_name', ['name'], { unique: true })
export class StageWorkLevel {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id!: number;

  @Column({ type: 'varchar', length: 20, nullable: false })
  name!: string;

  @Column({ type: 'tinytext', nullable: true })
  description?: string;
}
