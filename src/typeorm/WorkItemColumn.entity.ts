import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

export enum TableTarget {
  WORK_ITEMS = 'work_items',
  DRAWINGS = 'drawings',
  PACKAGES = 'packages',
  JOBS = 'jobs'
}

export enum TableSource {
  JOBS = 'Jobs',
  PACKAGES = 'Packages',
  SPOOLS = 'Spools',
  WORK_ITEMS = 'work_items',
  MATERIAL_TYPES = 'material_types',
  LAYDOWN_LOCATIONS = 'laydown_locations',
  JOINT_HEAT_NUMBERS = 'joint_heat_numbers',
  WORK_ITEM_CATEGORIES = 'work_item_categories',
  CONTAINERS = 'containers',
  JOINING_PROCEDURES = 'joining_procedures',
  REVIT_CONDUIT_BEND_INFORMATION = 'revit_conduit_bend_information',
  CUSTOM_COLUMNS_DATA = 'custom_columns_data'
}

@Entity('work_item_columns')
@Index('unq_table_target_table_source_name', ['table_target', 'table_source', 'name'], { unique: true })
@Index('idx_table_effect_id', ['table_effect_id'])
@Index('idx_table_target_deleted', ['table_target', 'deleted'])
@Index('idx_name_fk_column_deleted', ['name', 'fk_column', 'deleted'])
@Index('idx_fk_column_table_source_name', ['fk_column', 'table_source', 'name'])
@Index('idx_is_numeric_deleted', ['is_numeric', 'deleted'])
export class WorkItemColumn {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id!: number;

  @Column({ 
    type: 'enum', 
    enum: TableTarget, 
    default: TableTarget.WORK_ITEMS,
    comment: 'the frontend table this column belongs to'
  })
  table_target!: TableTarget;

  @Column({ type: 'varchar', length: 50, nullable: false })
  display_name!: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  name!: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  normal_name!: string;

  @Column({ type: 'varchar', length: 10, nullable: false, default: 'string' })
  data_type!: string;

  @Column({ 
    type: 'tinyint', 
    unsigned: true, 
    nullable: false, 
    default: 0,
    comment: 'columns like identifier, where instead applies to row'
  })
  visible_in_work_table!: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  table_effect_id?: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  color?: string;

  @Column({ 
    type: 'tinyint', 
    unsigned: true, 
    nullable: false, 
    default: 0,
    comment: 'allows for the column to be used to build stage conditions'
  })
  usable_for_conditions!: number;

  @Column({ 
    type: 'tinyint', 
    unsigned: true, 
    nullable: false, 
    default: 0,
    comment: '0 is count 1 is numeric 2 is fn_size_parser then numeric'
  })
  is_numeric!: number;

  @Column({ 
    type: 'enum', 
    enum: TableSource, 
    nullable: true,
    comment: 'table reference to a source table'
  })
  table_source?: TableSource;

  @Column({ type: 'varchar', length: 50, nullable: true })
  fk_column?: string;

  @Column({ 
    type: 'tinyint', 
    unsigned: true, 
    nullable: false, 
    default: 0,
    comment: 'is editable on my work page'
  })
  editable_my_work!: number;

  @Column({ 
    type: 'tinyint', 
    unsigned: true, 
    nullable: false, 
    default: 0,
    comment: 'is editable on jobs page'
  })
  editable!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  deleted!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  groupable!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  is_custom!: number;
}
