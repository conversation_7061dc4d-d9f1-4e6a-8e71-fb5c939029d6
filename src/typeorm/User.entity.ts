import { Entity, PrimaryGeneratedColumn, Column, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('Users')
@Index('unq_UserName', ['UserName'], { unique: true })
@Index('unq_employee_number', ['EmployeeNumber'], { unique: true })
@Index('idx_RoleID', ['RoleID'])
export class User {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id!: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  EmployeeNumber?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  WelderID?: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  UserName!: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  FirstName!: string;

  @Column({ type: 'varchar', length: 60, nullable: false })
  LastName!: string;

  @Column({ type: 'varchar', length: 120, nullable: true })
  Email?: string;

  @Column({ type: 'varchar', length: 30, nullable: true })
  PhoneNumber?: string;

  @Column({ type: 'varchar', length: 100, nullable: false, default: '' })
  Password!: string;

  @Column({ type: 'int', unsigned: true, nullable: true })
  GroupID?: number;

  @Column({ type: 'int', unsigned: true, nullable: false, default: 3 })
  RoleID!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 1 })
  isActive!: number;

  @CreateDateColumn({ type: 'timestamp', nullable: true })
  created?: Date;

  @Column({ type: 'int', unsigned: true, nullable: false })
  TierID!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  Hidden!: number;

  @Column({ type: 'decimal', precision: 4, scale: 2, nullable: true })
  LaborRate?: number;

  @Column({ type: 'tinytext', nullable: true })
  ProfilePhoto?: string;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  passChange!: number;

  @UpdateDateColumn({ type: 'timestamp', nullable: true })
  LastUpdated?: Date;

  @Column({ type: 'tinyint', unsigned: true, nullable: true, default: 0 })
  deleted?: number;

  @Column({ type: 'varchar', length: 4, nullable: true })
  Pin?: string;

  @Column({ type: 'tinyint', unsigned: true, nullable: false })
  user_Shifts!: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  notificationFrequency!: number;

  @Column({ type: 'int', unsigned: true, nullable: false, default: 1 })
  ShiftType!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  float_mode!: number;

  @Column({ type: 'varchar', length: 50, nullable: false, default: '1' })
  zoho_contact_id!: string;
}
