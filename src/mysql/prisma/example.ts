import { PrismaClient } from '@prisma/client';
import { PrismaStageBenchmark } from './StageBenchmark';

// Example usage of the Prisma Stage benchmark
async function runPrismaStageBenchmarkExample() {
  // Create Prisma Client instance
  const prisma = new PrismaClient({
    log: ['error'], // Set to ['query', 'info', 'warn', 'error'] for detailed logging
    datasources: {
      db: {
        url: process.env.DATABASE_URL || 'mysql://username:password@localhost:3306/accoc_staging'
      }
    }
  });

  try {
    // Test database connection
    await prisma.$connect();
    console.log('Prisma database connection established');

    // Create benchmark instance
    const benchmark = new PrismaStageBenchmark(prisma);

    // Run a single benchmark test
    console.log('\n=== Running Single Prisma Benchmark Test ===');
    const singleResult = await benchmark.runSingleBenchmark({
      name: `Prisma_Test_Stage_${Date.now()}`,
      stage_work_level_id: 1,
      shape: 'Square',
      stage_code_id: 1,
      metric_work_item_column_id: 1,
      locked: 0,
      bim: 1,
      fab: 1,
      field: 0,
      user_id: 1,
      nestable: 1,
      rejectable: 1,
      groupable: 1
    });

    console.log('Single test results:');
    console.log(`Stored Procedure:  ${singleResult.storedProcedure.executionTime.toFixed(2)}ms - ${singleResult.storedProcedure.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`Prisma Client:     ${singleResult.prismaClient.executionTime.toFixed(2)}ms - ${singleResult.prismaClient.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`Advanced Queries:  ${singleResult.advancedQueries.executionTime.toFixed(2)}ms - ${singleResult.advancedQueries.success ? 'SUCCESS' : 'FAILED'}`);

    // Run full benchmark suite
    console.log('\n=== Running Full Prisma Benchmark Suite ===');
    const results = await benchmark.runBenchmarkSuite(10); // 10 iterations

    // Print formatted results
    benchmark.printResults(results);

    // You can also access raw data for further analysis
    console.log('\n=== Raw Data Sample ===');
    console.log('First 3 stored procedure results:');
    results.storedProcedure.slice(0, 3).forEach((result, index) => {
      console.log(`  ${index + 1}: ${result.executionTime.toFixed(2)}ms - ${result.success ? 'SUCCESS' : 'FAILED'}`);
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
    });

    // Clean up test data
    console.log('\n=== Cleaning Up Test Data ===');
    await benchmark.cleanupTestData();

    // Example of direct Prisma operations for comparison
    console.log('\n=== Direct Prisma Operations Example ===');
    
    // Create a stage using Prisma Client directly
    const directStage = await prisma.stage.create({
      data: {
        name: `Direct_Prisma_Stage_${Date.now()}`,
        stage_work_level_id: 1,
        shape: 'Diamond',
        locked: 0,
        bim: 1,
        fab: 1,
        field: 0,
        nestable: 1,
        rejectable: 1,
        groupable: 1
      },
      include: {
        stageWorkLevel: true,
        stageCode: true,
        metricWorkItemColumn: true
      }
    });

    console.log('Direct Prisma stage created:', {
      id: directStage.id,
      name: directStage.name,
      workLevel: directStage.stageWorkLevel?.name
    });

    // Query stages with relations
    const stagesWithRelations = await prisma.stage.findMany({
      where: {
        deleted: 0,
        name: {
          contains: 'Prisma'
        }
      },
      include: {
        stageWorkLevel: true,
        stageCode: true,
        history: {
          take: 1,
          orderBy: {
            updated_on: 'desc'
          }
        }
      },
      take: 5
    });

    console.log(`Found ${stagesWithRelations.length} stages with relations`);

    // Example of Raw SQL query
    const stageCount = await prisma.$queryRaw<{count: number}[]>`
      SELECT COUNT(*) as count FROM stages WHERE deleted = 0
    `;
    console.log(`Total active stages (Raw SQL): ${stageCount[0].count}`);

    // Clean up the direct example stage
    await prisma.stage.delete({
      where: { id: directStage.id }
    });

  } catch (error) {
    console.error('Prisma benchmark failed:', error);
  } finally {
    // Clean up
    await prisma.$disconnect();
    console.log('\nPrisma database connection closed');
  }
}

// Export for use in other files
export { runPrismaStageBenchmarkExample };

// Uncomment to run directly
// runPrismaStageBenchmarkExample().catch(console.error);
