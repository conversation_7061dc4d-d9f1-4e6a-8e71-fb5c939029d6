import { PrismaClient, Prisma } from '@prisma/client';

export interface CreateStageParams {
  name: string;
  stage_work_level_id: number;
  shape?: string;
  stage_code_id?: number;
  metric_work_item_column_id?: number;
  locked: number;
  bim: number;
  fab: number;
  field: number;
  user_id: number;
  nestable: number;
  rejectable: number;
  groupable: number;
}

export interface CreateStageResult {
  status: 'success' | 'error';
  stage_id?: number;
  message: string;
}

export class PrismaStageService {
  constructor(private prisma: PrismaClient) {}

  // Method 1: Direct stored procedure call
  async createStageStoredProcedure(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      const result = await this.prisma.$queryRaw<any[]>`
        CALL sp_create_stage(
          ${params.name},
          ${params.stage_work_level_id},
          ${params.shape || null},
          ${params.stage_code_id || null},
          ${params.metric_work_item_column_id || null},
          ${params.locked},
          ${params.bim},
          ${params.fab},
          ${params.field},
          ${params.user_id},
          ${params.nestable},
          ${params.rejectable},
          ${params.groupable}
        )
      `;

      // The stored procedure returns a result set with status, stage_id, and message
      const spResult = result[0];
      
      return {
        status: spResult.status,
        stage_id: spResult.stage_id,
        message: spResult.message
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 2: Using Prisma Client (ORM entities)
  async createStageWithPrismaClient(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      return await this.prisma.$transaction(async (tx) => {
        // Step 1: Validate user exists and is active
        const userError = await this.validateUser(tx, params.user_id);
        if (userError) {
          throw new Error(userError);
        }

        // Step 2: Validate stage name
        const nameError = this.validateStageName(params.name);
        if (nameError) {
          throw new Error(nameError);
        }

        // Step 3: Check for unique name
        const uniquenessError = await this.validateStageNameUniqueness(tx, params.name);
        if (uniquenessError) {
          throw new Error(uniquenessError);
        }

        // Step 4: Validate stage_work_level_id is provided
        const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
        if (workLevelError) {
          throw new Error(workLevelError);
        }

        // Step 5: Check if metric column is custom (should not be allowed)
        const metricColumnId = await this.validateMetricColumn(tx, params.metric_work_item_column_id);

        // Step 6: Create the stage
        const stage = await tx.stage.create({
          data: {
            name: params.name,
            stage_work_level_id: params.stage_work_level_id,
            shape: params.shape as any,
            stage_code_id: params.stage_code_id,
            metric_work_item_column_id: metricColumnId,
            locked: params.locked,
            bim: params.bim,
            fab: params.fab,
            field: params.field,
            nestable: params.nestable,
            rejectable: params.rejectable,
            groupable: params.groupable
          }
        });

        if (!stage.id) {
          throw new Error('Failed to create stage');
        }

        // Step 7: Create history record
        await this.createStageHistoryRecord(tx, stage.id, params.user_id);

        return {
          status: 'success' as const,
          stage_id: stage.id,
          message: 'Stage created'
        };
      });

    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 3: Using Prisma Raw SQL (Query Builder equivalent)
  async createStageWithRawSQL(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      return await this.prisma.$transaction(async (tx) => {
        // Step 1: Validate user exists and is active using Raw SQL
        const userError = await this.validateUserWithRawSQL(tx, params.user_id);
        if (userError) {
          throw new Error(userError);
        }

        // Step 2: Validate stage name
        const nameError = this.validateStageName(params.name);
        if (nameError) {
          throw new Error(nameError);
        }

        // Step 3: Check for unique name using Raw SQL
        const uniquenessError = await this.validateStageNameUniquenessWithRawSQL(tx, params.name);
        if (uniquenessError) {
          throw new Error(uniquenessError);
        }

        // Step 4: Validate stage_work_level_id is provided
        const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
        if (workLevelError) {
          throw new Error(workLevelError);
        }

        // Step 5: Check if metric column is custom (should not be allowed) using Raw SQL
        const metricColumnId = await this.validateMetricColumnWithRawSQL(tx, params.metric_work_item_column_id);

        // Step 6: Insert the stage using Raw SQL
        const insertResult = await tx.$executeRaw`
          INSERT INTO stages (name, stage_work_level_id, shape, stage_code_id, metric_work_item_column_id, 
           locked, bim, fab, field, nestable, rejectable, groupable) 
           VALUES (${params.name}, ${params.stage_work_level_id}, ${params.shape || null}, 
           ${params.stage_code_id || null}, ${metricColumnId || null}, ${params.locked}, 
           ${params.bim}, ${params.fab}, ${params.field}, ${params.nestable}, 
           ${params.rejectable}, ${params.groupable})
        `;

        if (insertResult === 0) {
          throw new Error('Failed to create stage');
        }

        // Get the inserted stage ID
        const newStage = await tx.$queryRaw<{id: number}[]>`
          SELECT id FROM stages WHERE name = ${params.name} AND deleted = 0 ORDER BY id DESC LIMIT 1
        `;

        const newStageId = newStage[0]?.id;
        if (!newStageId) {
          throw new Error('Failed to retrieve created stage ID');
        }

        // Step 7: Create history record using Raw SQL
        await this.createStageHistoryWithRawSQL(tx, newStageId, params.user_id);

        return {
          status: 'success' as const,
          stage_id: newStageId,
          message: 'Stage created'
        };
      });

    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Private validation methods for Prisma Client
  private async validateUser(tx: Prisma.TransactionClient, userId: number): Promise<string | null> {
    const user = await tx.user.findFirst({
      where: {
        id: userId,
        deleted: 0,
        isActive: 1
      },
      select: { id: true }
    });

    return user ? null : 'User does not exist';
  }

  private validateStageName(name: string): string | null {
    if (!name || name.trim() === '') {
      return 'Name must be provided and must be unique';
    }
    return null;
  }

  private validateStageWorkLevelId(stageWorkLevelId: number): string | null {
    if (!stageWorkLevelId) {
      return 'stage_work_level_id is required';
    }
    return null;
  }

  private async validateStageNameUniqueness(tx: Prisma.TransactionClient, name: string): Promise<string | null> {
    const existingStage = await tx.stage.findFirst({
      where: {
        name: name,
        deleted: 0
      },
      select: { id: true }
    });

    return existingStage ? 'Name must be provided and must be unique' : null;
  }

  private async validateMetricColumn(tx: Prisma.TransactionClient, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    const workItemColumn = await tx.workItemColumn.findFirst({
      where: {
        id: metricColumnId,
        is_custom: 1
      },
      select: { id: true }
    });

    return workItemColumn ? undefined : metricColumnId;
  }

  private async createStageHistoryRecord(tx: Prisma.TransactionClient, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await tx.stageHistory.updateMany({
      where: {
        stage_id: stageId,
        active: 1
      },
      data: { active: 0 }
    });

    // Get the current stage data to create history record
    const stage = await tx.stage.findUnique({
      where: { id: stageId }
    });

    if (stage) {
      await tx.stageHistory.create({
        data: {
          stage_id: stage.id,
          name: stage.name,
          stage_work_level_id: stage.stage_work_level_id,
          shape: stage.shape || 'Square',
          stage_code_id: stage.stage_code_id,
          metric_work_item_column_id: stage.metric_work_item_column_id,
          locked: stage.locked,
          bim: stage.bim,
          fab: stage.fab,
          field: stage.field,
          deleted: stage.deleted,
          archived: stage.archived,
          nestable: stage.nestable,
          rejectable: stage.rejectable,
          shipping_block_id: stage.shipping_block_id,
          shipping_block_position: stage.shipping_block_position,
          groupable: stage.groupable,
          active: 1,
          updated_by: userId,
          updated_on: new Date()
        }
      });
    }
  }

  // Private validation methods for Raw SQL
  private async validateUserWithRawSQL(tx: Prisma.TransactionClient, userId: number): Promise<string | null> {
    const users = await tx.$queryRaw<{id: number}[]>`
      SELECT id FROM Users WHERE id = ${userId} AND deleted = 0 AND isActive = 1
    `;

    return users.length > 0 ? null : 'User does not exist';
  }

  private async validateStageNameUniquenessWithRawSQL(tx: Prisma.TransactionClient, name: string): Promise<string | null> {
    const stages = await tx.$queryRaw<{id: number}[]>`
      SELECT id FROM stages WHERE name = ${name} AND deleted = 0
    `;

    return stages.length > 0 ? 'Name must be provided and must be unique' : null;
  }

  private async validateMetricColumnWithRawSQL(tx: Prisma.TransactionClient, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    const customColumns = await tx.$queryRaw<{id: number}[]>`
      SELECT id FROM work_item_columns WHERE id = ${metricColumnId} AND is_custom = 1 AND deleted = 0
    `;

    return customColumns.length > 0 ? undefined : metricColumnId;
  }

  private async createStageHistoryWithRawSQL(tx: Prisma.TransactionClient, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await tx.$executeRaw`
      UPDATE stages_history SET active = 0 WHERE stage_id = ${stageId} AND active = 1
    `;

    // Insert new history record by selecting from the stages table
    await tx.$executeRaw`
      INSERT INTO stages_history (
        stage_id, name, stage_work_level_id, shape, stage_code_id, metric_work_item_column_id,
        locked, bim, fab, field, deleted, archived, nestable, rejectable,
        shipping_block_id, shipping_block_position, groupable, active, updated_by, updated_on
      )
      SELECT
        id, name, stage_work_level_id, shape, stage_code_id, metric_work_item_column_id,
        locked, bim, fab, field, deleted, archived, nestable, rejectable,
        shipping_block_id, shipping_block_position, groupable, 1, ${userId}, NOW()
      FROM stages
      WHERE id = ${stageId}
    `;
  }
}
