// Prisma Schema for Stage Management
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                Int      @id @default(autoincrement()) @db.UnsignedInt
  EmployeeNumber    String?  @db.VarChar(50)
  WelderID          String?  @db.VarChar(50)
  UserName          String   @unique @db.VarChar(100)
  FirstName         String   @db.VarChar(50)
  LastName          String   @db.VarChar(60)
  Email             String?  @db.VarChar(120)
  PhoneNumber       String?  @db.VarChar(30)
  Password          String   @default("") @db.VarChar(100)
  GroupID           Int?     @db.UnsignedInt
  RoleID            Int      @default(3) @db.UnsignedInt
  isActive          Int      @default(1) @db.UnsignedTinyInt
  created           DateTime? @default(now()) @db.Timestamp(0)
  TierID            Int      @db.UnsignedInt
  Hidden            Int      @default(0) @db.UnsignedTinyInt
  LaborRate         Decimal? @db.Decimal(4, 2)
  ProfilePhoto      String?  @db.TinyText
  passChange        Int      @default(0) @db.UnsignedTinyInt
  LastUpdated       DateTime? @updatedAt @db.Timestamp(0)
  deleted           Int?     @default(0) @db.UnsignedTinyInt
  Pin               String?  @db.VarChar(4)
  user_Shifts       Int      @db.UnsignedTinyInt
  notificationFrequency Int  @db.UnsignedInt
  ShiftType         Int      @default(1) @db.UnsignedInt
  float_mode        Int      @default(0) @db.UnsignedTinyInt
  zoho_contact_id   String   @default("1") @db.VarChar(50)

  // Relations
  createdStages     StageCode[]    @relation("StageCodeCreatedBy")
  updatedStages     StageCode[]    @relation("StageCodeUpdatedBy")
  deletedStages     StageCode[]    @relation("StageCodeDeletedBy")
  stageHistory      StageHistory[] @relation("StageHistoryUpdatedBy")

  @@unique([EmployeeNumber])
  @@index([RoleID])
  @@map("Users")
}

model StageWorkLevel {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  name        String  @unique @db.VarChar(20)
  description String? @db.TinyText

  // Relations
  stages Stage[]

  @@map("stage_work_levels")
}

model StageCode {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  name       String    @db.VarChar(190)
  created_by Int       @db.UnsignedInt
  created_on DateTime  @default(now()) @db.Timestamp(0)
  updated_by Int?      @db.UnsignedInt
  deleted    Int       @default(0) @db.UnsignedTinyInt
  deleted_by Int?      @db.UnsignedInt
  deleted_on DateTime? @db.Timestamp(0)

  // Relations
  createdByUser User?  @relation("StageCodeCreatedBy", fields: [created_by], references: [id])
  updatedByUser User?  @relation("StageCodeUpdatedBy", fields: [updated_by], references: [id])
  deletedByUser User?  @relation("StageCodeDeletedBy", fields: [deleted_by], references: [id])
  stages        Stage[]

  @@unique([name])
  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
  @@index([name, deleted])
  @@map("stage_codes")
}

enum TableTarget {
  work_items
  drawings
  packages
  jobs
}

enum TableSource {
  Jobs
  Packages
  Spools
  work_items
  material_types
  laydown_locations
  joint_heat_numbers
  work_item_categories
  containers
  joining_procedures
  revit_conduit_bend_information
  custom_columns_data
}

model WorkItemColumn {
  id                     Int          @id @default(autoincrement()) @db.UnsignedInt
  table_target           TableTarget  @default(work_items)
  display_name           String       @db.VarChar(50)
  name                   String       @db.VarChar(50)
  normal_name            String       @db.VarChar(50)
  data_type              String       @default("string") @db.VarChar(10)
  visible_in_work_table  Int          @default(0) @db.UnsignedTinyInt
  table_effect_id        Int?         @db.UnsignedInt
  color                  String?      @db.VarChar(50)
  usable_for_conditions  Int          @default(0) @db.UnsignedTinyInt
  is_numeric             Int          @default(0) @db.UnsignedTinyInt
  table_source           TableSource?
  fk_column              String?      @db.VarChar(50)
  editable_my_work       Int          @default(0) @db.UnsignedTinyInt
  editable               Int          @default(0) @db.UnsignedTinyInt
  deleted                Int          @default(0) @db.UnsignedTinyInt
  groupable              Int          @default(0) @db.UnsignedTinyInt
  is_custom              Int          @default(0) @db.UnsignedTinyInt

  // Relations
  stages Stage[]

  @@unique([table_target, table_source, name])
  @@index([table_effect_id])
  @@index([table_target, deleted])
  @@index([name, fk_column, deleted])
  @@index([fk_column, table_source, name])
  @@index([is_numeric, deleted])
  @@map("work_item_columns")
}

enum StageShape {
  Square
  Diamond
}

model Stage {
  id                         Int               @id @default(autoincrement()) @db.UnsignedInt
  name                       String            @db.VarChar(190)
  stage_work_level_id        Int               @db.UnsignedInt
  shape                      StageShape?
  stage_code_id              Int?              @db.UnsignedInt
  metric_work_item_column_id Int?              @db.UnsignedInt
  locked                     Int               @default(0) @db.UnsignedTinyInt
  bim                        Int               @default(0) @db.UnsignedTinyInt
  fab                        Int               @default(0) @db.UnsignedTinyInt
  field                      Int               @default(0) @db.UnsignedTinyInt
  deleted                    Int               @default(0) @db.UnsignedTinyInt
  archived                   Int               @default(0) @db.UnsignedTinyInt
  nestable                   Int               @default(0) @db.UnsignedTinyInt
  shipping_block_id          Int?              @db.UnsignedInt
  rejectable                 Int               @default(0) @db.UnsignedTinyInt
  shipping_block_position    Int?              @db.UnsignedInt
  groupable                  Int               @default(0) @db.UnsignedTinyInt
  stats_calculated           Int               @default(1) @db.UnsignedTinyInt

  // Relations
  stageWorkLevel        StageWorkLevel  @relation(fields: [stage_work_level_id], references: [id])
  stageCode             StageCode?      @relation(fields: [stage_code_id], references: [id])
  metricWorkItemColumn  WorkItemColumn? @relation(fields: [metric_work_item_column_id], references: [id])
  history               StageHistory[]

  @@index([stage_code_id])
  @@index([stage_work_level_id])
  @@index([metric_work_item_column_id])
  @@index([shipping_block_id])
  @@index([deleted, archived])
  @@map("stages")
}

model StageHistory {
  id                      Int        @id @default(autoincrement()) @db.UnsignedInt
  stage_id                Int        @db.UnsignedInt
  name                    String     @db.VarChar(190)
  stage_work_level_id     Int        @db.UnsignedInt
  shape                   StageShape @default(Square)
  stage_code_id           Int?       @db.UnsignedInt
  metric_work_item_column_id Int?    @db.UnsignedInt
  locked                  Int        @default(0) @db.UnsignedTinyInt
  bim                     Int        @default(0) @db.UnsignedTinyInt
  fab                     Int        @default(0) @db.UnsignedTinyInt
  field                   Int        @default(0) @db.UnsignedTinyInt
  deleted                 Int        @default(0) @db.UnsignedTinyInt
  archived                Int        @default(0) @db.UnsignedTinyInt
  active                  Int        @default(1) @db.UnsignedTinyInt
  updated_by              Int        @db.UnsignedInt
  updated_on              DateTime   @default(now()) @db.DateTime(0)
  nestable                Int        @default(0) @db.UnsignedTinyInt
  rejectable              Int        @default(0) @db.UnsignedTinyInt
  shipping_block_id       Int?       @db.UnsignedInt
  shipping_block_position Int?       @db.UnsignedInt
  groupable               Int        @default(0) @db.UnsignedTinyInt

  // Relations
  stage         Stage @relation(fields: [stage_id], references: [id])
  updatedByUser User  @relation("StageHistoryUpdatedBy", fields: [updated_by], references: [id])

  @@index([updated_by])
  @@index([stage_id, active])
  @@map("stages_history")
}
