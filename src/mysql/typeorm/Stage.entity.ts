import { Entity, PrimaryGeneratedColumn, Column, Index, ManyToOne, JoinC<PERSON>umn, OneToMany } from 'typeorm';
import { StageWorkLevel } from './StageWorkLevel.entity';
import { StageCode } from './StageCode.entity';
import { WorkItemColumn } from './WorkItemColumn.entity';
import { StageHistory } from './StageHistory.entity';

export enum StageShape {
  SQUARE = 'Square',
  DIAMOND = 'Diamond'
}

@Entity('stages')
@Index('idx_stage_code_id', ['stage_code_id'])
@Index('idx_stage_work_level_id', ['stage_work_level_id'])
@Index('idx_metric_work_item_column_id', ['metric_work_item_column_id'])
@Index('idx_shipping_block_id', ['shipping_block_id'])
@Index('idx_deleted_archived', ['deleted', 'archived'])
export class Stage {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id!: number;

  @Column({ type: 'varchar', length: 190, nullable: false })
  name!: string;

  @Column({ type: 'int', unsigned: true, nullable: false })
  stage_work_level_id!: number;

  @Column({ type: 'enum', enum: StageShape, nullable: true })
  shape?: StageShape;

  @Column({ type: 'int', unsigned: true, nullable: true })
  stage_code_id?: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  metric_work_item_column_id?: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  locked!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  bim!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  fab!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  field!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  deleted!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  archived!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  nestable!: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  shipping_block_id?: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  rejectable!: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  shipping_block_position?: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  groupable!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 1 })
  stats_calculated!: number;

  // Relations
  @ManyToOne(() => StageWorkLevel)
  @JoinColumn({ name: 'stage_work_level_id' })
  stageWorkLevel?: StageWorkLevel;

  @ManyToOne(() => StageCode)
  @JoinColumn({ name: 'stage_code_id' })
  stageCode?: StageCode;

  @ManyToOne(() => WorkItemColumn)
  @JoinColumn({ name: 'metric_work_item_column_id' })
  metricWorkItemColumn?: WorkItemColumn;

  @OneToMany(() => StageHistory, stageHistory => stageHistory.stage)
  history?: StageHistory[];
}
