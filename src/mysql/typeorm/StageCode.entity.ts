import { Entity, PrimaryGeneratedColumn, Column, Index, ManyToOne, JoinColumn, CreateDateColumn } from 'typeorm';
import { User } from './User.entity';

@Entity('stage_codes')
@Index('idx_name', ['name'], { unique: true })
@Index('idx_created_by', ['created_by'])
@Index('idx_deleted_by', ['deleted_by'])
@Index('idx_updated_by', ['updated_by'])
@Index('idx_name_deleted', ['name', 'deleted'])
export class StageCode {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id!: number;

  @Column({ type: 'varchar', length: 190, nullable: false })
  name!: string;

  @Column({ type: 'int', unsigned: true, nullable: false })
  created_by!: number;

  @CreateDateColumn({ type: 'timestamp', nullable: false, default: () => 'CURRENT_TIMESTAMP' })
  created_on!: Date;

  @Column({ type: 'int', unsigned: true, nullable: true })
  updated_by?: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  deleted!: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  deleted_by?: number;

  @Column({ type: 'timestamp', nullable: true })
  deleted_on?: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  createdByUser?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'deleted_by' })
  deletedByUser?: User;
}
