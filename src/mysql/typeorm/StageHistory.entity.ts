import { Entity, PrimaryGeneratedColumn, Column, Index, ManyToOne, JoinColumn, CreateDateColumn } from 'typeorm';
import { Stage, StageShape } from './Stage.entity';
import { User } from './User.entity';

@Entity('stages_history')
@Index('idx_updated_by', ['updated_by'])
@Index('idx_stage_id_active', ['stage_id', 'active'])
export class StageHistory {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id!: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  stage_id!: number;

  @Column({ type: 'varchar', length: 190, nullable: false })
  name!: string;

  @Column({ type: 'int', unsigned: true, nullable: false })
  stage_work_level_id!: number;

  @Column({ type: 'enum', enum: StageShape, nullable: false, default: StageShape.SQUARE })
  shape!: StageShape;

  @Column({ type: 'int', unsigned: true, nullable: true })
  stage_code_id?: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  metric_work_item_column_id?: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  locked!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  bim!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  fab!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  field!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  deleted!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  archived!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 1 })
  active!: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  updated_by!: number;

  @CreateDateColumn({ type: 'datetime', nullable: false, default: () => 'CURRENT_TIMESTAMP' })
  updated_on!: Date;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  nestable!: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  rejectable!: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  shipping_block_id?: number;

  @Column({ type: 'int', unsigned: true, nullable: true })
  shipping_block_position?: number;

  @Column({ type: 'tinyint', unsigned: true, nullable: false, default: 0 })
  groupable!: number;

  // Relations
  @ManyToOne(() => Stage, stage => stage.history)
  @JoinColumn({ name: 'stage_id' })
  stage?: Stage;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser?: User;
}
