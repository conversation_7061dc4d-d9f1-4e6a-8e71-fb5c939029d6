import { DataSource } from 'typeorm';
import { StageService, CreateStageParams, CreateStageResult } from './StageService';

export interface BenchmarkResult {
  method: string;
  executionTime: number;
  success: boolean;
  result?: CreateStageResult;
  error?: string;
}

export interface BenchmarkSummary {
  storedProcedure: BenchmarkResult[];
  entities: BenchmarkResult[];
  queryBuilder: BenchmarkResult[];
  averages: {
    storedProcedure: number;
    entities: number;
    queryBuilder: number;
  };
}

export class StageBenchmark {
  private stageService: StageService;

  constructor(private dataSource: DataSource) {
    this.stageService = new StageService(dataSource);
  }

  // Generate test data for benchmarking
  private generateTestParams(index: number): CreateStageParams {
    return {
      name: `Benchmark_Stage_${Date.now()}_${index}`,
      stage_work_level_id: 1, // Assuming this exists
      shape: index % 2 === 0 ? 'Square' : 'Diamond',
      stage_code_id: 1, // Assuming this exists
      metric_work_item_column_id: 1, // Assuming this exists
      locked: 0,
      bim: index % 3 === 0 ? 1 : 0,
      fab: 1,
      field: index % 2,
      user_id: 1, // Assuming this user exists
      nestable: 1,
      rejectable: 1,
      groupable: 1
    };
  }

  // Benchmark a single method execution
  private async benchmarkMethod(
    method: () => Promise<CreateStageResult>,
    methodName: string
  ): Promise<BenchmarkResult> {
    const startTime = process.hrtime.bigint();
    
    try {
      const result = await method();
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1_000_000; // Convert to milliseconds

      return {
        method: methodName,
        executionTime,
        success: result.status === 'success',
        result
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1_000_000;

      return {
        method: methodName,
        executionTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Run benchmark for all three methods
  async runSingleBenchmark(params: CreateStageParams): Promise<{
    storedProcedure: BenchmarkResult;
    entities: BenchmarkResult;
    queryBuilder: BenchmarkResult;
  }> {
    // Test stored procedure
    const storedProcedure = await this.benchmarkMethod(
      () => this.stageService.createStageStoredProcedure(params),
      'Stored Procedure'
    );

    // Modify params for next test to avoid name conflicts
    const entitiesParams = { ...params, name: `${params.name}_entities` };
    const entities = await this.benchmarkMethod(
      () => this.stageService.createStageWithEntities(entitiesParams),
      'TypeORM Entities'
    );

    // Modify params for next test to avoid name conflicts
    const queryBuilderParams = { ...params, name: `${params.name}_qb` };
    const queryBuilder = await this.benchmarkMethod(
      () => this.stageService.createStageWithQueryBuilder(queryBuilderParams),
      'Query Builder'
    );

    return {
      storedProcedure,
      entities,
      queryBuilder
    };
  }

  // Run multiple iterations and collect statistics
  async runBenchmarkSuite(iterations: number = 10): Promise<BenchmarkSummary> {
    const results: BenchmarkSummary = {
      storedProcedure: [],
      entities: [],
      queryBuilder: [],
      averages: {
        storedProcedure: 0,
        entities: 0,
        queryBuilder: 0
      }
    };

    console.log(`Starting benchmark suite with ${iterations} iterations...`);

    for (let i = 0; i < iterations; i++) {
      console.log(`Running iteration ${i + 1}/${iterations}`);
      
      const params = this.generateTestParams(i);
      const benchmarkResult = await this.runSingleBenchmark(params);

      results.storedProcedure.push(benchmarkResult.storedProcedure);
      results.entities.push(benchmarkResult.entities);
      results.queryBuilder.push(benchmarkResult.queryBuilder);

      // Small delay between iterations to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Calculate averages (only for successful executions)
    const successfulSP = results.storedProcedure.filter(r => r.success);
    const successfulEntities = results.entities.filter(r => r.success);
    const successfulQB = results.queryBuilder.filter(r => r.success);

    results.averages.storedProcedure = successfulSP.length > 0 
      ? successfulSP.reduce((sum, r) => sum + r.executionTime, 0) / successfulSP.length 
      : 0;

    results.averages.entities = successfulEntities.length > 0 
      ? successfulEntities.reduce((sum, r) => sum + r.executionTime, 0) / successfulEntities.length 
      : 0;

    results.averages.queryBuilder = successfulQB.length > 0 
      ? successfulQB.reduce((sum, r) => sum + r.executionTime, 0) / successfulQB.length 
      : 0;

    return results;
  }

  // Print benchmark results in a formatted way
  printResults(results: BenchmarkSummary): void {
    console.log('\n=== BENCHMARK RESULTS ===');
    console.log(`\nAverage Execution Times (ms):`);
    console.log(`Stored Procedure: ${results.averages.storedProcedure.toFixed(2)}ms`);
    console.log(`TypeORM Entities: ${results.averages.entities.toFixed(2)}ms`);
    console.log(`Query Builder:    ${results.averages.queryBuilder.toFixed(2)}ms`);

    console.log(`\nSuccess Rates:`);
    console.log(`Stored Procedure: ${results.storedProcedure.filter(r => r.success).length}/${results.storedProcedure.length}`);
    console.log(`TypeORM Entities: ${results.entities.filter(r => r.success).length}/${results.entities.length}`);
    console.log(`Query Builder:    ${results.queryBuilder.filter(r => r.success).length}/${results.queryBuilder.length}`);

    // Performance comparison
    const fastest = Math.min(
      results.averages.storedProcedure,
      results.averages.entities,
      results.averages.queryBuilder
    );

    console.log(`\nPerformance Comparison (relative to fastest):`);
    if (results.averages.storedProcedure > 0) {
      console.log(`Stored Procedure: ${(results.averages.storedProcedure / fastest).toFixed(2)}x`);
    }
    if (results.averages.entities > 0) {
      console.log(`TypeORM Entities: ${(results.averages.entities / fastest).toFixed(2)}x`);
    }
    if (results.averages.queryBuilder > 0) {
      console.log(`Query Builder:    ${(results.averages.queryBuilder / fastest).toFixed(2)}x`);
    }
  }
}
