import { DataSource, Repository } from 'typeorm';
import { Stage } from './Stage.entity';
import { User } from './User.entity';
import { WorkItemColumn } from './WorkItemColumn.entity';
import { StageHistory } from './StageHistory.entity';

export interface CreateStageParams {
  name: string;
  stage_work_level_id: number;
  shape?: string;
  stage_code_id?: number;
  metric_work_item_column_id?: number;
  locked: number;
  bim: number;
  fab: number;
  field: number;
  user_id: number;
  nestable: number;
  rejectable: number;
  groupable: number;
}

export interface CreateStageResult {
  status: 'success' | 'error';
  stage_id?: number;
  message: string;
}

export class StageService {
  private stageRepository: Repository<Stage>;
  private userRepository: Repository<User>;
  private workItemColumnRepository: Repository<WorkItemColumn>;
  private stageHistoryRepository: Repository<StageHistory>;

  constructor(private dataSource: DataSource) {
    this.stageRepository = dataSource.getRepository(Stage);
    this.userRepository = dataSource.getRepository(User);
    this.workItemColumnRepository = dataSource.getRepository(WorkItemColumn);
    this.stageHistoryRepository = dataSource.getRepository(StageHistory);
  }

  // Method 1: Direct stored procedure call
  async createStageStoredProcedure(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      const result = await this.dataSource.query(
        `CALL sp_create_stage(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          params.name,
          params.stage_work_level_id,
          params.shape || null,
          params.stage_code_id || null,
          params.metric_work_item_column_id || null,
          params.locked,
          params.bim,
          params.fab,
          params.field,
          params.user_id,
          params.nestable,
          params.rejectable,
          params.groupable
        ]
      );

      // The stored procedure returns a result set with status, stage_id, and message
      const spResult = result[0][0];

      return {
        status: spResult.status,
        stage_id: spResult.stage_id,
        message: spResult.message
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 2: Using TypeORM entities
  async createStageWithEntities(params: CreateStageParams): Promise<CreateStageResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Step 1: Validate user exists and is active
      const user = await queryRunner.manager.findOne(User, {
        where: {
          id: params.user_id,
          deleted: 0,
          isActive: 1
        }
      });

      if (!user) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'User does not exist'
        };
      }

      // Step 2: Check for unique name
      const existingStage = await queryRunner.manager.findOne(Stage, {
        where: {
          name: params.name,
          deleted: 0
        }
      });

      if (existingStage || !params.name || params.name.trim() === '') {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Name must be provided and must be unique'
        };
      }

      // Step 3: Validate stage_work_level_id is provided
      if (!params.stage_work_level_id) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'stage_work_level_id is required'
        };
      }

      // Step 4: Check if metric column is custom (should not be allowed)
      let metricColumnId = params.metric_work_item_column_id;
      if (metricColumnId) {
        const workItemColumn = await queryRunner.manager.findOne(WorkItemColumn, {
          where: {
            id: metricColumnId,
            is_custom: 1
          }
        });

        if (workItemColumn) {
          metricColumnId = undefined; // Set to null if custom column
        }
      }

      // Step 5: Create the stage
      const stage = new Stage();
      stage.name = params.name;
      stage.stage_work_level_id = params.stage_work_level_id;
      stage.shape = params.shape as any;
      stage.stage_code_id = params.stage_code_id;
      stage.metric_work_item_column_id = metricColumnId;
      stage.locked = params.locked;
      stage.bim = params.bim;
      stage.fab = params.fab;
      stage.field = params.field;
      stage.nestable = params.nestable;
      stage.rejectable = params.rejectable;
      stage.groupable = params.groupable;

      const savedStage = await queryRunner.manager.save(Stage, stage);

      if (!savedStage.id) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Failed to create stage'
        };
      }

      // Step 6: Create history record
      await this.createStageHistoryRecord(queryRunner, savedStage.id, params.user_id);

      await queryRunner.commitTransaction();

      return {
        status: 'success',
        stage_id: savedStage.id,
        message: 'Stage created'
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Method 3: Using TypeORM Query Builder
  async createStageWithQueryBuilder(params: CreateStageParams): Promise<CreateStageResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Step 1: Validate user exists and is active using Query Builder
      const userExists = await queryRunner.manager
        .createQueryBuilder(User, 'user')
        .select(['user.id'])
        .where('user.id = :userId', { userId: params.user_id })
        .andWhere('user.deleted = :deleted', { deleted: 0 })
        .andWhere('user.isActive = :isActive', { isActive: 1 })
        .getOne();

      if (!userExists) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'User does not exist'
        };
      }

      // Step 2: Check for unique name and validate required inputs
      if (!params.name || params.name.trim() === '') {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Name must be provided and must be unique'
        };
      }

      const existingStage = await queryRunner.manager
        .createQueryBuilder(Stage, 'stage')
        .select(['stage.id'])
        .where('stage.name = :name', { name: params.name })
        .andWhere('stage.deleted = :deleted', { deleted: 0 })
        .getOne();

      if (existingStage) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Name must be provided and must be unique'
        };
      }

      // Step 3: Validate stage_work_level_id is provided
      if (!params.stage_work_level_id) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'stage_work_level_id is required'
        };
      }

      // Step 4: Check if metric column is custom (should not be allowed)
      let metricColumnId = params.metric_work_item_column_id;
      if (metricColumnId) {
        const customColumn = await queryRunner.manager
          .createQueryBuilder(WorkItemColumn, 'column')
          .select(['column.id', 'column.is_custom'])
          .where('column.id = :columnId', { columnId: metricColumnId })
          .andWhere('column.is_custom = :isCustom', { isCustom: 1 })
          .andWhere('column.deleted = :deleted', { deleted: 0 })
          .getOne();

        if (customColumn) {
          metricColumnId = undefined; // Set to null if custom column
        }
      }

      // Step 5: Insert the stage using Query Builder
      const insertResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(Stage)
        .values({
          name: params.name,
          stage_work_level_id: params.stage_work_level_id,
          shape: params.shape as any,
          stage_code_id: params.stage_code_id,
          metric_work_item_column_id: metricColumnId,
          locked: params.locked,
          bim: params.bim,
          fab: params.fab,
          field: params.field,
          nestable: params.nestable,
          rejectable: params.rejectable,
          groupable: params.groupable
        })
        .execute();

      const newStageId = insertResult.identifiers[0]?.id;

      if (!newStageId) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Failed to create stage'
        };
      }

      // Step 6: Create history record using Query Builder
      await this.createStageHistoryWithQueryBuilder(queryRunner, newStageId, params.user_id);

      await queryRunner.commitTransaction();

      return {
        status: 'success',
        stage_id: newStageId,
        message: 'Stage created'
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Helper method to create stage history record
  private async createStageHistoryRecord(queryRunner: any, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await queryRunner.manager.update(StageHistory,
      { stage_id: stageId, active: 1 },
      { active: 0 }
    );

    // Get the current stage data to create history record
    const stage = await queryRunner.manager.findOne(Stage, {
      where: { id: stageId }
    });

    if (stage) {
      const stageHistory = new StageHistory();
      stageHistory.stage_id = stage.id;
      stageHistory.name = stage.name;
      stageHistory.stage_work_level_id = stage.stage_work_level_id;
      stageHistory.shape = stage.shape as any;
      stageHistory.stage_code_id = stage.stage_code_id;
      stageHistory.metric_work_item_column_id = stage.metric_work_item_column_id;
      stageHistory.locked = stage.locked;
      stageHistory.bim = stage.bim;
      stageHistory.fab = stage.fab;
      stageHistory.field = stage.field;
      stageHistory.deleted = stage.deleted;
      stageHistory.archived = stage.archived;
      stageHistory.nestable = stage.nestable;
      stageHistory.rejectable = stage.rejectable;
      stageHistory.shipping_block_id = stage.shipping_block_id;
      stageHistory.shipping_block_position = stage.shipping_block_position;
      stageHistory.groupable = stage.groupable;
      stageHistory.active = 1;
      stageHistory.updated_by = userId;
      stageHistory.updated_on = new Date();

      await queryRunner.manager.save(StageHistory, stageHistory);
    }
  }

  // Helper method to create stage history record using Query Builder
  private async createStageHistoryWithQueryBuilder(queryRunner: any, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage using Query Builder
    await queryRunner.manager
      .createQueryBuilder()
      .update(StageHistory)
      .set({ active: 0 })
      .where('stage_id = :stageId', { stageId })
      .andWhere('active = :active', { active: 1 })
      .execute();

    // Get the current stage data using Query Builder
    const stage = await queryRunner.manager
      .createQueryBuilder(Stage, 'stage')
      .where('stage.id = :stageId', { stageId })
      .getOne();

    if (stage) {
      // Insert new history record using Query Builder
      await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(StageHistory)
        .values({
          stage_id: stage.id,
          name: stage.name,
          stage_work_level_id: stage.stage_work_level_id,
          shape: stage.shape,
          stage_code_id: stage.stage_code_id,
          metric_work_item_column_id: stage.metric_work_item_column_id,
          locked: stage.locked,
          bim: stage.bim,
          fab: stage.fab,
          field: stage.field,
          deleted: stage.deleted,
          archived: stage.archived,
          nestable: stage.nestable,
          rejectable: stage.rejectable,
          shipping_block_id: stage.shipping_block_id,
          shipping_block_position: stage.shipping_block_position,
          groupable: stage.groupable,
          active: 1,
          updated_by: userId,
          updated_on: new Date()
        })
        .execute();
    }
  }
}
