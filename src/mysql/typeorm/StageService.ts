import { DataSource, Repository } from 'typeorm';
import { Stage } from './Stage.entity';
import { User } from './User.entity';
import { WorkItemColumn } from './WorkItemColumn.entity';
import { StageHistory } from './StageHistory.entity';

export interface CreateStageParams {
  name: string;
  stage_work_level_id: number;
  shape?: string;
  stage_code_id?: number;
  metric_work_item_column_id?: number;
  locked: number;
  bim: number;
  fab: number;
  field: number;
  user_id: number;
  nestable: number;
  rejectable: number;
  groupable: number;
}

export interface CreateStageResult {
  status: 'success' | 'error';
  stage_id?: number;
  message: string;
}

export class StageService {
  private stageRepository: Repository<Stage>;
  private userRepository: Repository<User>;
  private workItemColumnRepository: Repository<WorkItemColumn>;
  private stageHistoryRepository: Repository<StageHistory>;

  constructor(private dataSource: DataSource) {
    this.stageRepository = dataSource.getRepository(Stage);
    this.userRepository = dataSource.getRepository(User);
    this.workItemColumnRepository = dataSource.getRepository(WorkItemColumn);
    this.stageHistoryRepository = dataSource.getRepository(StageHistory);
  }

  // Method 1: Direct stored procedure call
  async createStageStoredProcedure(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      const result = await this.dataSource.query(
        `CALL sp_create_stage(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          params.name,
          params.stage_work_level_id,
          params.shape || null,
          params.stage_code_id || null,
          params.metric_work_item_column_id || null,
          params.locked,
          params.bim,
          params.fab,
          params.field,
          params.user_id,
          params.nestable,
          params.rejectable,
          params.groupable
        ]
      );

      // The stored procedure returns a result set with status, stage_id, and message
      const spResult = result[0][0];

      return {
        status: spResult.status,
        stage_id: spResult.stage_id,
        message: spResult.message
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 2: Using TypeORM entities
  async createStageWithEntities(params: CreateStageParams): Promise<CreateStageResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Step 1: Validate user exists and is active
      const user = await queryRunner.manager.findOne(User, {
        where: {
          id: params.user_id,
          deleted: 0,
          isActive: 1
        }
      });

      if (!user) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'User does not exist'
        };
      }

      // Step 2: Check for unique name
      const existingStage = await queryRunner.manager.findOne(Stage, {
        where: {
          name: params.name,
          deleted: 0
        }
      });

      if (existingStage || !params.name || params.name.trim() === '') {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Name must be provided and must be unique'
        };
      }

      // Step 3: Validate stage_work_level_id is provided
      if (!params.stage_work_level_id) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'stage_work_level_id is required'
        };
      }

      // Step 4: Check if metric column is custom (should not be allowed)
      let metricColumnId = params.metric_work_item_column_id;
      if (metricColumnId) {
        const workItemColumn = await queryRunner.manager.findOne(WorkItemColumn, {
          where: {
            id: metricColumnId,
            is_custom: 1
          }
        });

        if (workItemColumn) {
          metricColumnId = undefined; // Set to null if custom column
        }
      }

      // Step 5: Create the stage
      const stage = new Stage();
      stage.name = params.name;
      stage.stage_work_level_id = params.stage_work_level_id;
      stage.shape = params.shape as any;
      stage.stage_code_id = params.stage_code_id;
      stage.metric_work_item_column_id = metricColumnId;
      stage.locked = params.locked;
      stage.bim = params.bim;
      stage.fab = params.fab;
      stage.field = params.field;
      stage.nestable = params.nestable;
      stage.rejectable = params.rejectable;
      stage.groupable = params.groupable;

      const savedStage = await queryRunner.manager.save(Stage, stage);

      if (!savedStage.id) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Failed to create stage'
        };
      }

      // Step 6: Create history record
      await this.createStageHistoryRecord(queryRunner, savedStage.id, params.user_id);

      await queryRunner.commitTransaction();

      return {
        status: 'success',
        stage_id: savedStage.id,
        message: 'Stage created'
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Method 3: Using SQL query builder
  async createStageWithQueryBuilder(params: CreateStageParams): Promise<CreateStageResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Step 1: Validate user exists and is active
      const userCheck = await queryRunner.query(
        `SELECT id FROM Users WHERE id = ? AND deleted = 0 AND isActive = 1`,
        [params.user_id]
      );

      if (userCheck.length === 0) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'User does not exist'
        };
      }

      // Step 2: Check for unique name and validate required inputs
      if (!params.name || params.name.trim() === '') {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Name must be provided and must be unique'
        };
      }

      const nameCheck = await queryRunner.query(
        `SELECT id FROM stages WHERE name = ? AND deleted = 0`,
        [params.name]
      );

      if (nameCheck.length > 0) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Name must be provided and must be unique'
        };
      }

      // Step 3: Validate stage_work_level_id is provided
      if (!params.stage_work_level_id) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'stage_work_level_id is required'
        };
      }

      // Step 4: Check if metric column is custom (should not be allowed)
      let metricColumnId = params.metric_work_item_column_id;
      if (metricColumnId) {
        const customColumnCheck = await queryRunner.query(
          `SELECT id FROM work_item_columns WHERE id = ? AND is_custom = 1`,
          [metricColumnId]
        );

        if (customColumnCheck.length > 0) {
          metricColumnId = undefined; // Set to null if custom column
        }
      }

      // Step 5: Insert the stage
      const insertResult = await queryRunner.query(
        `INSERT INTO stages (name, stage_work_level_id, shape, stage_code_id, metric_work_item_column_id,
         locked, bim, fab, field, nestable, rejectable, groupable)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          params.name,
          params.stage_work_level_id,
          params.shape || null,
          params.stage_code_id || null,
          metricColumnId || null,
          params.locked,
          params.bim,
          params.fab,
          params.field,
          params.nestable,
          params.rejectable,
          params.groupable
        ]
      );

      const newStageId = insertResult.insertId;

      if (!newStageId) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Failed to create stage'
        };
      }

      // Step 6: Create history record using SQL
      await this.createStageHistoryWithSQL(queryRunner, newStageId, params.user_id);

      await queryRunner.commitTransaction();

      return {
        status: 'success',
        stage_id: newStageId,
        message: 'Stage created'
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Helper method to create stage history record
  private async createStageHistoryRecord(queryRunner: any, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await queryRunner.manager.update(StageHistory,
      { stage_id: stageId, active: 1 },
      { active: 0 }
    );

    // Get the current stage data to create history record
    const stage = await queryRunner.manager.findOne(Stage, {
      where: { id: stageId }
    });

    if (stage) {
      const stageHistory = new StageHistory();
      stageHistory.stage_id = stage.id;
      stageHistory.name = stage.name;
      stageHistory.stage_work_level_id = stage.stage_work_level_id;
      stageHistory.shape = stage.shape as any;
      stageHistory.stage_code_id = stage.stage_code_id;
      stageHistory.metric_work_item_column_id = stage.metric_work_item_column_id;
      stageHistory.locked = stage.locked;
      stageHistory.bim = stage.bim;
      stageHistory.fab = stage.fab;
      stageHistory.field = stage.field;
      stageHistory.deleted = stage.deleted;
      stageHistory.archived = stage.archived;
      stageHistory.nestable = stage.nestable;
      stageHistory.rejectable = stage.rejectable;
      stageHistory.shipping_block_id = stage.shipping_block_id;
      stageHistory.shipping_block_position = stage.shipping_block_position;
      stageHistory.groupable = stage.groupable;
      stageHistory.active = 1;
      stageHistory.updated_by = userId;
      stageHistory.updated_on = new Date();

      await queryRunner.manager.save(StageHistory, stageHistory);
    }
  }

  // Helper method to create stage history record using SQL
  private async createStageHistoryWithSQL(queryRunner: any, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await queryRunner.query(
      `UPDATE stages_history SET active = 0 WHERE stage_id = ? AND active = 1`,
      [stageId]
    );

    // Insert new history record by selecting from the stages table
    await queryRunner.query(
      `INSERT INTO stages_history (
        stage_id, name, stage_work_level_id, shape, stage_code_id, metric_work_item_column_id,
        locked, bim, fab, field, deleted, archived, nestable, rejectable,
        shipping_block_id, shipping_block_position, groupable, active, updated_by, updated_on
      )
      SELECT
        id, name, stage_work_level_id, shape, stage_code_id, metric_work_item_column_id,
        locked, bim, fab, field, deleted, archived, nestable, rejectable,
        shipping_block_id, shipping_block_position, groupable, 1, ?, NOW()
      FROM stages
      WHERE id = ?`,
      [userId, stageId]
    );
  }
}
