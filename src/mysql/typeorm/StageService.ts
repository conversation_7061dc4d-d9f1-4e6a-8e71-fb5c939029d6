import { DataSource, Repository } from 'typeorm';
import { Stage } from './Stage.entity';
import { User } from './User.entity';
import { WorkItemColumn } from './WorkItemColumn.entity';
import { StageHistory } from './StageHistory.entity';

export interface CreateStageParams {
  name: string;
  stage_work_level_id: number;
  shape?: string;
  stage_code_id?: number;
  metric_work_item_column_id?: number;
  locked: number;
  bim: number;
  fab: number;
  field: number;
  user_id: number;
  nestable: number;
  rejectable: number;
  groupable: number;
}

export interface CreateStageResult {
  status: 'success' | 'error';
  stage_id?: number;
  message: string;
}

export class StageService {
  private stageRepository: Repository<Stage>;
  private userRepository: Repository<User>;
  private workItemColumnRepository: Repository<WorkItemColumn>;
  private stageHistoryRepository: Repository<StageHistory>;

  constructor(private dataSource: DataSource) {
    this.stageRepository = dataSource.getRepository(Stage);
    this.userRepository = dataSource.getRepository(User);
    this.workItemColumnRepository = dataSource.getRepository(WorkItemColumn);
    this.stageHistoryRepository = dataSource.getRepository(StageHistory);
  }

  // Method 1: Direct stored procedure call
  async createStageStoredProcedure(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      const result = await this.dataSource.query(
        `CALL sp_create_stage(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          params.name,
          params.stage_work_level_id,
          params.shape || null,
          params.stage_code_id || null,
          params.metric_work_item_column_id || null,
          params.locked,
          params.bim,
          params.fab,
          params.field,
          params.user_id,
          params.nestable,
          params.rejectable,
          params.groupable
        ]
      );

      // The stored procedure returns a result set with status, stage_id, and message
      const spResult = result[0][0];

      return {
        status: spResult.status,
        stage_id: spResult.stage_id,
        message: spResult.message
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 2: Using TypeORM entities
  async createStageWithEntities(params: CreateStageParams): Promise<CreateStageResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Step 1: Validate user exists and is active
      const userError = await this.validateUser(queryRunner, params.user_id);
      if (userError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: userError
        };
      }

      // Step 2: Validate stage name
      const nameError = this.validateStageName(params.name);
      if (nameError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: nameError
        };
      }

      // Step 3: Check for unique name
      const uniquenessError = await this.validateStageNameUniqueness(queryRunner, params.name);
      if (uniquenessError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: uniquenessError
        };
      }

      // Step 4: Validate stage_work_level_id is provided
      const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
      if (workLevelError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: workLevelError
        };
      }

      // Step 5: Check if metric column is custom (should not be allowed)
      const metricColumnId = await this.validateMetricColumn(queryRunner, params.metric_work_item_column_id);

      // Step 6: Create the stage
      const stage = new Stage();
      stage.name = params.name;
      stage.stage_work_level_id = params.stage_work_level_id;
      stage.shape = params.shape as any;
      stage.stage_code_id = params.stage_code_id;
      stage.metric_work_item_column_id = metricColumnId;
      stage.locked = params.locked;
      stage.bim = params.bim;
      stage.fab = params.fab;
      stage.field = params.field;
      stage.nestable = params.nestable;
      stage.rejectable = params.rejectable;
      stage.groupable = params.groupable;

      const savedStage = await queryRunner.manager.save(Stage, stage);

      if (!savedStage.id) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Failed to create stage'
        };
      }

      // Step 7: Create history record
      await this.createStageHistoryRecord(queryRunner, savedStage.id, params.user_id);

      await queryRunner.commitTransaction();

      return {
        status: 'success',
        stage_id: savedStage.id,
        message: 'Stage created'
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Method 3: Using TypeORM Query Builder (Proper Implementation)
  // This method uses TypeORM's Query Builder API instead of raw SQL
  // Benefits: Type safety, better integration with TypeORM, cleaner syntax
  async createStageWithQueryBuilder(params: CreateStageParams): Promise<CreateStageResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Step 1: Validate user exists and is active using Query Builder
      const userError = await this.validateUserWithQueryBuilder(queryRunner, params.user_id);
      if (userError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: userError
        };
      }

      // Step 2: Validate stage name
      const nameError = this.validateStageName(params.name);
      if (nameError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: nameError
        };
      }

      // Step 3: Check for unique name using Query Builder
      const uniquenessError = await this.validateStageNameUniquenessWithQueryBuilder(queryRunner, params.name);
      if (uniquenessError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: uniquenessError
        };
      }

      // Step 4: Validate stage_work_level_id is provided
      const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
      if (workLevelError) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: workLevelError
        };
      }

      // Step 5: Check if metric column is custom (should not be allowed) using Query Builder
      const metricColumnId = await this.validateMetricColumnWithQueryBuilder(queryRunner, params.metric_work_item_column_id);

      // Step 6: Insert the stage using Query Builder
      const insertResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(Stage)
        .values({
          name: params.name,
          stage_work_level_id: params.stage_work_level_id,
          shape: params.shape as any,
          stage_code_id: params.stage_code_id,
          metric_work_item_column_id: metricColumnId,
          locked: params.locked,
          bim: params.bim,
          fab: params.fab,
          field: params.field,
          nestable: params.nestable,
          rejectable: params.rejectable,
          groupable: params.groupable
        })
        .execute();

      const newStageId = insertResult.identifiers[0]?.id;

      if (!newStageId) {
        await queryRunner.rollbackTransaction();
        return {
          status: 'error',
          message: 'Failed to create stage'
        };
      }

      // Step 7: Create history record using Query Builder
      await this.createStageHistoryWithQueryBuilder(queryRunner, newStageId, params.user_id);

      await queryRunner.commitTransaction();

      return {
        status: 'success',
        stage_id: newStageId,
        message: 'Stage created'
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Helper method to create stage history record
  private async createStageHistoryRecord(queryRunner: any, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await queryRunner.manager.update(StageHistory,
      { stage_id: stageId, active: 1 },
      { active: 0 }
    );

    // Get the current stage data to create history record
    const stage = await queryRunner.manager.findOne(Stage, {
      where: { id: stageId }
    });

    if (stage) {
      const stageHistory = new StageHistory();
      stageHistory.stage_id = stage.id;
      stageHistory.name = stage.name;
      stageHistory.stage_work_level_id = stage.stage_work_level_id;
      stageHistory.shape = stage.shape as any;
      stageHistory.stage_code_id = stage.stage_code_id;
      stageHistory.metric_work_item_column_id = stage.metric_work_item_column_id;
      stageHistory.locked = stage.locked;
      stageHistory.bim = stage.bim;
      stageHistory.fab = stage.fab;
      stageHistory.field = stage.field;
      stageHistory.deleted = stage.deleted;
      stageHistory.archived = stage.archived;
      stageHistory.nestable = stage.nestable;
      stageHistory.rejectable = stage.rejectable;
      stageHistory.shipping_block_id = stage.shipping_block_id;
      stageHistory.shipping_block_position = stage.shipping_block_position;
      stageHistory.groupable = stage.groupable;
      stageHistory.active = 1;
      stageHistory.updated_by = userId;
      stageHistory.updated_on = new Date();

      await queryRunner.manager.save(StageHistory, stageHistory);
    }
  }

  // Private validation methods
  private async validateUser(queryRunner: any, userId: number): Promise<string | null> {
    const user = await queryRunner.manager.findOne(User, {
      where: {
        id: userId,
        deleted: 0,
        isActive: 1
      }
    });

    return user ? null : 'User does not exist';
  }

  private async validateUserWithQueryBuilder(queryRunner: any, userId: number): Promise<string | null> {
    const userExists = await queryRunner.manager
      .createQueryBuilder(User, 'user')
      .select(['user.id'])
      .where('user.id = :userId', { userId })
      .andWhere('user.deleted = :deleted', { deleted: 0 })
      .andWhere('user.isActive = :isActive', { isActive: 1 })
      .getOne();

    return userExists ? null : 'User does not exist';
  }

  private validateStageName(name: string): string | null {
    if (!name || name.trim() === '') {
      return 'Name must be provided and must be unique';
    }
    return null;
  }

  private validateStageWorkLevelId(stageWorkLevelId: number): string | null {
    if (!stageWorkLevelId) {
      return 'stage_work_level_id is required';
    }
    return null;
  }

  private async validateStageNameUniqueness(queryRunner: any, name: string): Promise<string | null> {
    const existingStage = await queryRunner.manager.findOne(Stage, {
      where: {
        name: name,
        deleted: 0
      }
    });

    return existingStage ? 'Name must be provided and must be unique' : null;
  }

  private async validateStageNameUniquenessWithQueryBuilder(queryRunner: any, name: string): Promise<string | null> {
    const existingStage = await queryRunner.manager
      .createQueryBuilder(Stage, 'stage')
      .select(['stage.id'])
      .where('stage.name = :name', { name })
      .andWhere('stage.deleted = :deleted', { deleted: 0 })
      .getOne();

    return existingStage ? 'Name must be provided and must be unique' : null;
  }

  private async validateMetricColumn(queryRunner: any, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    const workItemColumn = await queryRunner.manager.findOne(WorkItemColumn, {
      where: {
        id: metricColumnId,
        is_custom: 1
      }
    });

    return workItemColumn ? undefined : metricColumnId;
  }

  private async validateMetricColumnWithQueryBuilder(queryRunner: any, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    const customColumn = await queryRunner.manager
      .createQueryBuilder(WorkItemColumn, 'column')
      .select(['column.id', 'column.is_custom'])
      .where('column.id = :columnId', { columnId: metricColumnId })
      .andWhere('column.is_custom = :isCustom', { isCustom: 1 })
      .andWhere('column.deleted = :deleted', { deleted: 0 })
      .getOne();

    return customColumn ? undefined : metricColumnId;
  }

  // Helper method to create stage history record using Query Builder
  private async createStageHistoryWithQueryBuilder(queryRunner: any, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage using Query Builder
    await queryRunner.manager
      .createQueryBuilder()
      .update(StageHistory)
      .set({ active: 0 })
      .where('stage_id = :stageId', { stageId })
      .andWhere('active = :active', { active: 1 })
      .execute();

    // Get the current stage data using Query Builder
    const stage = await queryRunner.manager
      .createQueryBuilder(Stage, 'stage')
      .where('stage.id = :stageId', { stageId })
      .getOne();

    if (stage) {
      // Insert new history record using Query Builder
      await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(StageHistory)
        .values({
          stage_id: stage.id,
          name: stage.name,
          stage_work_level_id: stage.stage_work_level_id,
          shape: stage.shape,
          stage_code_id: stage.stage_code_id,
          metric_work_item_column_id: stage.metric_work_item_column_id,
          locked: stage.locked,
          bim: stage.bim,
          fab: stage.fab,
          field: stage.field,
          deleted: stage.deleted,
          archived: stage.archived,
          nestable: stage.nestable,
          rejectable: stage.rejectable,
          shipping_block_id: stage.shipping_block_id,
          shipping_block_position: stage.shipping_block_position,
          groupable: stage.groupable,
          active: 1,
          updated_by: userId,
          updated_on: new Date()
        })
        .execute();
    }
  }
}
