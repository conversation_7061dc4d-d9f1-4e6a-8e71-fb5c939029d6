import { <PERSON><PERSON>ty, PrimaryKey, Property, ManyToOne, Index, OneToMany, Collection } from '@mikro-orm/core';
import { User } from './User.entity';
import { Stage } from './Stage.entity';

@Entity({ tableName: 'stage_codes' })
@Index({ name: 'unq_name', properties: ['name'], options: { unique: true } })
@Index({ name: 'idx_created_by', properties: ['created_by'] })
@Index({ name: 'idx_updated_by', properties: ['updated_by'] })
@Index({ name: 'idx_deleted_by', properties: ['deleted_by'] })
@Index({ name: 'idx_name_deleted', properties: ['name', 'deleted'] })
export class StageCode {
  @PrimaryKey({ type: 'int', unsigned: true })
  id!: number;

  @Property({ type: 'varchar', length: 190 })
  name!: string;

  @Property({ type: 'int', unsigned: true })
  created_by!: number;

  @Property({ type: 'timestamp', onCreate: () => new Date() })
  created_on!: Date;

  @Property({ type: 'int', unsigned: true, nullable: true })
  updated_by?: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  deleted!: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  deleted_by?: number;

  @Property({ type: 'timestamp', nullable: true })
  deleted_on?: Date;

  // Relations
  @ManyToOne(() => User, { joinColumn: 'created_by' })
  createdByUser!: User;

  @ManyToOne(() => User, { joinColumn: 'updated_by', nullable: true })
  updatedByUser?: User;

  @ManyToOne(() => User, { joinColumn: 'deleted_by', nullable: true })
  deletedByUser?: User;

  @OneToMany(() => Stage, stage => stage.stageCode)
  stages = new Collection<Stage>(this);
}
