import { Entity, PrimaryKey, Property, ManyToOne, Enum, Index, OneToMany, Collection } from '@mikro-orm/core';
import { StageWorkLevel } from './StageWorkLevel.entity';
import { StageCode } from './StageCode.entity';
import { WorkItemColumn } from './WorkItemColumn.entity';
import { StageHistory } from './StageHistory.entity';

export enum StageShape {
  SQUARE = 'Square',
  DIAMOND = 'Diamond'
}

@Entity({ tableName: 'stages' })
@Index({ name: 'idx_stage_code_id', properties: ['stage_code_id'] })
@Index({ name: 'idx_stage_work_level_id', properties: ['stage_work_level_id'] })
@Index({ name: 'idx_metric_work_item_column_id', properties: ['metric_work_item_column_id'] })
@Index({ name: 'idx_shipping_block_id', properties: ['shipping_block_id'] })
@Index({ name: 'idx_deleted_archived', properties: ['deleted', 'archived'] })
export class Stage {
  @PrimaryKey({ type: 'int', unsigned: true })
  id!: number;

  @Property({ type: 'varchar', length: 190 })
  name!: string;

  @Property({ type: 'int', unsigned: true })
  stage_work_level_id!: number;

  @Enum(() => StageShape)
  @Property({ type: 'enum', items: () => StageShape, nullable: true })
  shape?: StageShape;

  @Property({ type: 'int', unsigned: true, nullable: true })
  stage_code_id?: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  metric_work_item_column_id?: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  locked!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  bim!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  fab!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  field!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  deleted!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  archived!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  nestable!: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  shipping_block_id?: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  rejectable!: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  shipping_block_position?: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  groupable!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 1 })
  stats_calculated!: number;

  // Relations
  @ManyToOne(() => StageWorkLevel, { joinColumn: 'stage_work_level_id' })
  stageWorkLevel!: StageWorkLevel;

  @ManyToOne(() => StageCode, { joinColumn: 'stage_code_id', nullable: true })
  stageCode?: StageCode;

  @ManyToOne(() => WorkItemColumn, { joinColumn: 'metric_work_item_column_id', nullable: true })
  metricWorkItemColumn?: WorkItemColumn;

  @OneToMany(() => StageHistory, stageHistory => stageHistory.stage)
  history = new Collection<StageHistory>(this);
}
