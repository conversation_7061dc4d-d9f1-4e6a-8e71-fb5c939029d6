import { Entity, PrimaryKey, Property, Enum, Index, OneToMany, Collection } from '@mikro-orm/core';
import { Stage } from './Stage.entity';

export enum TableTarget {
  WORK_ITEMS = 'work_items',
  DRAWINGS = 'drawings',
  PACKAGES = 'packages',
  JOBS = 'jobs'
}

export enum TableSource {
  JOBS = 'Jobs',
  PACKAGES = 'Packages',
  SPOOLS = 'Spools',
  WORK_ITEMS = 'work_items',
  MATERIAL_TYPES = 'material_types',
  LAYDOWN_LOCATIONS = 'laydown_locations',
  JOINT_HEAT_NUMBERS = 'joint_heat_numbers',
  WORK_ITEM_CATEGORIES = 'work_item_categories',
  CONTAINERS = 'containers',
  JOINING_PROCEDURES = 'joining_procedures',
  REVIT_CONDUIT_BEND_INFORMATION = 'revit_conduit_bend_information',
  CUSTOM_COLUMNS_DATA = 'custom_columns_data'
}

@Entity({ tableName: 'work_item_columns' })
@Index({ name: 'unq_table_target_source_name', properties: ['table_target', 'table_source', 'name'], options: { unique: true } })
@Index({ name: 'idx_table_effect_id', properties: ['table_effect_id'] })
@Index({ name: 'idx_table_target_deleted', properties: ['table_target', 'deleted'] })
@Index({ name: 'idx_name_fk_column_deleted', properties: ['name', 'fk_column', 'deleted'] })
@Index({ name: 'idx_fk_column_table_source_name', properties: ['fk_column', 'table_source', 'name'] })
@Index({ name: 'idx_is_numeric_deleted', properties: ['is_numeric', 'deleted'] })
export class WorkItemColumn {
  @PrimaryKey({ type: 'int', unsigned: true })
  id!: number;

  @Enum(() => TableTarget)
  @Property({ type: 'enum', items: () => TableTarget, default: TableTarget.WORK_ITEMS })
  table_target!: TableTarget;

  @Property({ type: 'varchar', length: 50 })
  display_name!: string;

  @Property({ type: 'varchar', length: 50 })
  name!: string;

  @Property({ type: 'varchar', length: 50 })
  normal_name!: string;

  @Property({ type: 'varchar', length: 10, default: 'string' })
  data_type!: string;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  visible_in_work_table!: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  table_effect_id?: number;

  @Property({ type: 'varchar', length: 50, nullable: true })
  color?: string;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  usable_for_conditions!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  is_numeric!: number;

  @Enum(() => TableSource)
  @Property({ type: 'enum', items: () => TableSource, nullable: true })
  table_source?: TableSource;

  @Property({ type: 'varchar', length: 50, nullable: true })
  fk_column?: string;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  editable_my_work!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  editable!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  deleted!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  groupable!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  is_custom!: number;

  // Relations
  @OneToMany(() => Stage, stage => stage.metricWorkItemColumn)
  stages = new Collection<Stage>(this);
}
