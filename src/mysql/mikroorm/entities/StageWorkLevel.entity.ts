import { Entity, PrimaryKey, Property, Index, OneToMany, Collection } from '@mikro-orm/core';
import { Stage } from './Stage.entity';

@Entity({ tableName: 'stage_work_levels' })
@Index({ name: 'unq_name', properties: ['name'], options: { unique: true } })
export class StageWorkLevel {
  @PrimaryKey({ type: 'int', unsigned: true })
  id!: number;

  @Property({ type: 'varchar', length: 20 })
  name!: string;

  @Property({ type: 'text', nullable: true })
  description?: string;

  // Relations
  @OneToMany(() => Stage, stage => stage.stageWorkLevel)
  stages = new Collection<Stage>(this);
}
