import { PrismaClient } from '@prisma/client';
import { PrismaStageService, CreateStageParams, CreateStageResult } from './StageService';

export interface BenchmarkResult {
  method: string;
  executionTime: number;
  success: boolean;
  result?: CreateStageResult;
  error?: string;
}

export interface BenchmarkSummary {
  storedProcedure: BenchmarkResult[];
  prismaClient: BenchmarkResult[];
  advancedQueries: BenchmarkResult[];
  averages: {
    storedProcedure: number;
    prismaClient: number;
    advancedQueries: number;
  };
}

export class PrismaStageBenchmark {
  private stageService: PrismaStageService;

  constructor(private prisma: PrismaClient) {
    this.stageService = new PrismaStageService(prisma);
  }

  // Generate test data for benchmarking
  private generateTestParams(index: number): CreateStageParams {
    return {
      name: `Prisma_Benchmark_Stage_${Date.now()}_${index}`,
      stage_work_level_id: 1, // Assuming this exists
      shape: index % 2 === 0 ? 'Square' : 'Diamond',
      stage_code_id: 1, // Assuming this exists
      metric_work_item_column_id: 1, // Assuming this exists
      locked: 0,
      bim: index % 3 === 0 ? 1 : 0,
      fab: 1,
      field: index % 2,
      user_id: 1, // Assuming this user exists
      nestable: 1,
      rejectable: 1,
      groupable: 1
    };
  }

  // Benchmark a single method execution
  private async benchmarkMethod(
    method: () => Promise<CreateStageResult>,
    methodName: string
  ): Promise<BenchmarkResult> {
    const startTime = process.hrtime.bigint();
    
    try {
      const result = await method();
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1_000_000; // Convert to milliseconds

      return {
        method: methodName,
        executionTime,
        success: result.status === 'success',
        result
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1_000_000;

      return {
        method: methodName,
        executionTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Run benchmark for all three methods
  async runSingleBenchmark(params: CreateStageParams): Promise<{
    storedProcedure: BenchmarkResult;
    prismaClient: BenchmarkResult;
    advancedQueries: BenchmarkResult;
  }> {
    // Test stored procedure
    const storedProcedure = await this.benchmarkMethod(
      () => this.stageService.createStageStoredProcedure(params),
      'Stored Procedure'
    );

    // Modify params for next test to avoid name conflicts
    const prismaClientParams = { ...params, name: `${params.name}_prisma` };
    const prismaClient = await this.benchmarkMethod(
      () => this.stageService.createStageWithPrismaClient(prismaClientParams),
      'Prisma Client'
    );

    // Modify params for next test to avoid name conflicts
    const advancedQueriesParams = { ...params, name: `${params.name}_advanced` };
    const advancedQueries = await this.benchmarkMethod(
      () => this.stageService.createStageWithAdvancedQueries(advancedQueriesParams),
      'Advanced Queries'
    );

    return {
      storedProcedure,
      prismaClient,
      advancedQueries
    };
  }

  // Run multiple iterations and collect statistics
  async runBenchmarkSuite(iterations: number = 10): Promise<BenchmarkSummary> {
    const results: BenchmarkSummary = {
      storedProcedure: [],
      prismaClient: [],
      advancedQueries: [],
      averages: {
        storedProcedure: 0,
        prismaClient: 0,
        advancedQueries: 0
      }
    };

    console.log(`Starting Prisma benchmark suite with ${iterations} iterations...`);

    for (let i = 0; i < iterations; i++) {
      console.log(`Running iteration ${i + 1}/${iterations}`);

      const params = this.generateTestParams(i);
      const benchmarkResult = await this.runSingleBenchmark(params);

      results.storedProcedure.push(benchmarkResult.storedProcedure);
      results.prismaClient.push(benchmarkResult.prismaClient);
      results.advancedQueries.push(benchmarkResult.advancedQueries);

      // Small delay between iterations to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Calculate averages (only for successful executions)
    const successfulSP = results.storedProcedure.filter(r => r.success);
    const successfulPrisma = results.prismaClient.filter(r => r.success);
    const successfulAdvanced = results.advancedQueries.filter(r => r.success);

    results.averages.storedProcedure = successfulSP.length > 0
      ? successfulSP.reduce((sum, r) => sum + r.executionTime, 0) / successfulSP.length
      : 0;

    results.averages.prismaClient = successfulPrisma.length > 0
      ? successfulPrisma.reduce((sum, r) => sum + r.executionTime, 0) / successfulPrisma.length
      : 0;

    results.averages.advancedQueries = successfulAdvanced.length > 0
      ? successfulAdvanced.reduce((sum, r) => sum + r.executionTime, 0) / successfulAdvanced.length
      : 0;

    return results;
  }

  // Print benchmark results in a formatted way
  printResults(results: BenchmarkSummary): void {
    console.log('\n=== PRISMA BENCHMARK RESULTS ===');
    console.log(`\nAverage Execution Times (ms):`);
    console.log(`Stored Procedure:  ${results.averages.storedProcedure.toFixed(2)}ms`);
    console.log(`Prisma Client:     ${results.averages.prismaClient.toFixed(2)}ms`);
    console.log(`Advanced Queries:  ${results.averages.advancedQueries.toFixed(2)}ms`);

    console.log(`\nSuccess Rates:`);
    console.log(`Stored Procedure:  ${results.storedProcedure.filter(r => r.success).length}/${results.storedProcedure.length}`);
    console.log(`Prisma Client:     ${results.prismaClient.filter(r => r.success).length}/${results.prismaClient.length}`);
    console.log(`Advanced Queries:  ${results.advancedQueries.filter(r => r.success).length}/${results.advancedQueries.length}`);

    // Performance comparison
    const fastest = Math.min(
      results.averages.storedProcedure,
      results.averages.prismaClient,
      results.averages.advancedQueries
    );

    console.log(`\nPerformance Comparison (relative to fastest):`);
    if (results.averages.storedProcedure > 0) {
      console.log(`Stored Procedure:  ${(results.averages.storedProcedure / fastest).toFixed(2)}x`);
    }
    if (results.averages.prismaClient > 0) {
      console.log(`Prisma Client:     ${(results.averages.prismaClient / fastest).toFixed(2)}x`);
    }
    if (results.averages.advancedQueries > 0) {
      console.log(`Advanced Queries:  ${(results.averages.advancedQueries / fastest).toFixed(2)}x`);
    }
  }

  // Clean up test data (optional utility method)
  async cleanupTestData(): Promise<void> {
    try {
      // Delete test stages created during benchmarking
      await this.prisma.stage.deleteMany({
        where: {
          name: {
            contains: 'Prisma_Benchmark_Stage_'
          }
        }
      });

      // Delete test stage history
      await this.prisma.stageHistory.deleteMany({
        where: {
          name: {
            contains: 'Prisma_Benchmark_Stage_'
          }
        }
      });

      console.log('Test data cleaned up successfully');
    } catch (error) {
      console.error('Error cleaning up test data:', error);
    }
  }
}
