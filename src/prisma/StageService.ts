import { PrismaClient, Prisma } from '@prisma/client';

export interface CreateStageParams {
  name: string;
  stage_work_level_id: number;
  shape?: string;
  stage_code_id?: number;
  metric_work_item_column_id?: number;
  locked: number;
  bim: number;
  fab: number;
  field: number;
  user_id: number;
  nestable: number;
  rejectable: number;
  groupable: number;
}

export interface CreateStageResult {
  status: 'success' | 'error';
  stage_id?: number;
  message: string;
}

export class PrismaStageService {
  constructor(private prisma: PrismaClient) {}

  // Method 1: Direct stored procedure call
  async createStageStoredProcedure(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      const result = await this.prisma.$queryRaw<any[]>`
        CALL sp_create_stage(
          ${params.name},
          ${params.stage_work_level_id},
          ${params.shape || null},
          ${params.stage_code_id || null},
          ${params.metric_work_item_column_id || null},
          ${params.locked},
          ${params.bim},
          ${params.fab},
          ${params.field},
          ${params.user_id},
          ${params.nestable},
          ${params.rejectable},
          ${params.groupable}
        )
      `;

      // The stored procedure returns a result set with status, stage_id, and message
      const spResult = result[0];
      
      return {
        status: spResult.status,
        stage_id: spResult.stage_id,
        message: spResult.message
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 2: Using Prisma Client (ORM entities)
  async createStageWithPrismaClient(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      return await this.prisma.$transaction(async (tx) => {
        // Step 1: Validate user exists and is active
        const userError = await this.validateUser(tx, params.user_id);
        if (userError) {
          throw new Error(userError);
        }

        // Step 2: Validate stage name
        const nameError = this.validateStageName(params.name);
        if (nameError) {
          throw new Error(nameError);
        }

        // Step 3: Check for unique name
        const uniquenessError = await this.validateStageNameUniqueness(tx, params.name);
        if (uniquenessError) {
          throw new Error(uniquenessError);
        }

        // Step 4: Validate stage_work_level_id is provided
        const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
        if (workLevelError) {
          throw new Error(workLevelError);
        }

        // Step 5: Check if metric column is custom (should not be allowed)
        const metricColumnId = await this.validateMetricColumn(tx, params.metric_work_item_column_id);

        // Step 6: Create the stage
        const stage = await tx.stage.create({
          data: {
            name: params.name,
            stage_work_level_id: params.stage_work_level_id,
            shape: params.shape as any,
            stage_code_id: params.stage_code_id,
            metric_work_item_column_id: metricColumnId,
            locked: params.locked,
            bim: params.bim,
            fab: params.fab,
            field: params.field,
            nestable: params.nestable,
            rejectable: params.rejectable,
            groupable: params.groupable
          }
        });

        if (!stage.id) {
          throw new Error('Failed to create stage');
        }

        // Step 7: Create history record
        await this.createStageHistoryRecord(tx, stage.id, params.user_id);

        return {
          status: 'success' as const,
          stage_id: stage.id,
          message: 'Stage created'
        };
      });

    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 3: Using Prisma Advanced Query Building (Fluent API)
  async createStageWithAdvancedQueries(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      return await this.prisma.$transaction(async (tx) => {
        // Step 1: Validate user exists and is active using advanced queries
        const userError = await this.validateUserWithAdvancedQuery(tx, params.user_id);
        if (userError) {
          throw new Error(userError);
        }

        // Step 2: Validate stage name
        const nameError = this.validateStageName(params.name);
        if (nameError) {
          throw new Error(nameError);
        }

        // Step 3: Check for unique name using advanced queries
        const uniquenessError = await this.validateStageNameUniquenessWithAdvancedQuery(tx, params.name);
        if (uniquenessError) {
          throw new Error(uniquenessError);
        }

        // Step 4: Validate stage_work_level_id is provided
        const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
        if (workLevelError) {
          throw new Error(workLevelError);
        }

        // Step 5: Check if metric column is custom using advanced queries
        const metricColumnId = await this.validateMetricColumnWithAdvancedQuery(tx, params.metric_work_item_column_id);

        // Step 6: Create the stage using Prisma's advanced create with select
        const newStage = await tx.stage.create({
          data: {
            name: params.name,
            stage_work_level_id: params.stage_work_level_id,
            shape: params.shape as any,
            stage_code_id: params.stage_code_id,
            metric_work_item_column_id: metricColumnId,
            locked: params.locked,
            bim: params.bim,
            fab: params.fab,
            field: params.field,
            nestable: params.nestable,
            rejectable: params.rejectable,
            groupable: params.groupable
          },
          select: {
            id: true,
            name: true,
            stage_work_level_id: true,
            shape: true,
            stage_code_id: true,
            metric_work_item_column_id: true,
            locked: true,
            bim: true,
            fab: true,
            field: true,
            deleted: true,
            archived: true,
            nestable: true,
            rejectable: true,
            shipping_block_id: true,
            shipping_block_position: true,
            groupable: true
          }
        });

        if (!newStage.id) {
          throw new Error('Failed to create stage');
        }

        // Step 7: Create history record using advanced queries
        await this.createStageHistoryWithAdvancedQuery(tx, newStage, params.user_id);

        return {
          status: 'success' as const,
          stage_id: newStage.id,
          message: 'Stage created'
        };
      });

    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Private validation methods for Prisma Client
  private async validateUser(tx: Prisma.TransactionClient, userId: number): Promise<string | null> {
    const user = await tx.user.findFirst({
      where: {
        id: userId,
        deleted: 0,
        isActive: 1
      },
      select: { id: true }
    });

    return user ? null : 'User does not exist';
  }

  private validateStageName(name: string): string | null {
    if (!name || name.trim() === '') {
      return 'Name must be provided and must be unique';
    }
    return null;
  }

  private validateStageWorkLevelId(stageWorkLevelId: number): string | null {
    if (!stageWorkLevelId) {
      return 'stage_work_level_id is required';
    }
    return null;
  }

  private async validateStageNameUniqueness(tx: Prisma.TransactionClient, name: string): Promise<string | null> {
    const existingStage = await tx.stage.findFirst({
      where: {
        name: name,
        deleted: 0
      },
      select: { id: true }
    });

    return existingStage ? 'Name must be provided and must be unique' : null;
  }

  private async validateMetricColumn(tx: Prisma.TransactionClient, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    const workItemColumn = await tx.workItemColumn.findFirst({
      where: {
        id: metricColumnId,
        is_custom: 1
      },
      select: { id: true }
    });

    return workItemColumn ? undefined : metricColumnId;
  }

  private async createStageHistoryRecord(tx: Prisma.TransactionClient, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await tx.stageHistory.updateMany({
      where: {
        stage_id: stageId,
        active: 1
      },
      data: { active: 0 }
    });

    // Get the current stage data to create history record
    const stage = await tx.stage.findUnique({
      where: { id: stageId }
    });

    if (stage) {
      await tx.stageHistory.create({
        data: {
          stage_id: stage.id,
          name: stage.name,
          stage_work_level_id: stage.stage_work_level_id,
          shape: stage.shape || 'Square',
          stage_code_id: stage.stage_code_id,
          metric_work_item_column_id: stage.metric_work_item_column_id,
          locked: stage.locked,
          bim: stage.bim,
          fab: stage.fab,
          field: stage.field,
          deleted: stage.deleted,
          archived: stage.archived,
          nestable: stage.nestable,
          rejectable: stage.rejectable,
          shipping_block_id: stage.shipping_block_id,
          shipping_block_position: stage.shipping_block_position,
          groupable: stage.groupable,
          active: 1,
          updated_by: userId,
          updated_on: new Date()
        }
      });
    }
  }

  // Private validation methods for Advanced Queries (Prisma Query Builder-like)
  private async validateUserWithAdvancedQuery(tx: Prisma.TransactionClient, userId: number): Promise<string | null> {
    // Using Prisma's advanced where conditions and aggregation
    const userCount = await tx.user.count({
      where: {
        AND: [
          { id: userId },
          { deleted: 0 },
          { isActive: 1 }
        ]
      }
    });

    return userCount > 0 ? null : 'User does not exist';
  }

  private async validateStageNameUniquenessWithAdvancedQuery(tx: Prisma.TransactionClient, name: string): Promise<string | null> {
    // Using Prisma's advanced where conditions with count
    const stageCount = await tx.stage.count({
      where: {
        AND: [
          { name: { equals: name } },
          { deleted: { equals: 0 } }
        ]
      }
    });

    return stageCount > 0 ? 'Name must be provided and must be unique' : null;
  }

  private async validateMetricColumnWithAdvancedQuery(tx: Prisma.TransactionClient, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    // Using Prisma's advanced where conditions with complex logic
    const customColumnExists = await tx.workItemColumn.findFirst({
      where: {
        AND: [
          { id: metricColumnId },
          { is_custom: 1 },
          { deleted: 0 }
        ]
      },
      select: { id: true }
    });

    return customColumnExists ? undefined : metricColumnId;
  }

  private async createStageHistoryWithAdvancedQuery(
    tx: Prisma.TransactionClient,
    stageData: any,
    userId: number
  ): Promise<void> {
    // First, deactivate any existing active history records using updateMany
    await tx.stageHistory.updateMany({
      where: {
        AND: [
          { stage_id: stageData.id },
          { active: 1 }
        ]
      },
      data: { active: 0 }
    });

    // Create new history record using the stage data we already have
    await tx.stageHistory.create({
      data: {
        stage_id: stageData.id,
        name: stageData.name,
        stage_work_level_id: stageData.stage_work_level_id,
        shape: stageData.shape || 'Square',
        stage_code_id: stageData.stage_code_id,
        metric_work_item_column_id: stageData.metric_work_item_column_id,
        locked: stageData.locked,
        bim: stageData.bim,
        fab: stageData.fab,
        field: stageData.field,
        deleted: stageData.deleted,
        archived: stageData.archived,
        nestable: stageData.nestable,
        rejectable: stageData.rejectable,
        shipping_block_id: stageData.shipping_block_id,
        shipping_block_position: stageData.shipping_block_position,
        groupable: stageData.groupable,
        active: 1,
        updated_by: userId,
        updated_on: new Date()
      }
    });
  }
}
