import { Entity, PrimaryKey, Property, Index, OneToMany, Collection } from '@mikro-orm/core';
import { StageCode } from './StageCode.entity';
import { StageHistory } from './StageHistory.entity';

@Entity({ tableName: 'Users' })
@Index({ name: 'unq_UserName', properties: ['UserName'], options: { unique: true } })
@Index({ name: 'unq_employee_number', properties: ['EmployeeNumber'], options: { unique: true } })
@Index({ name: 'idx_RoleID', properties: ['RoleID'] })
export class User {
  @PrimaryKey({ type: 'int', unsigned: true })
  id!: number;

  @Property({ type: 'varchar', length: 50, nullable: true })
  EmployeeNumber?: string;

  @Property({ type: 'varchar', length: 50, nullable: true })
  WelderID?: string;

  @Property({ type: 'varchar', length: 100 })
  UserName!: string;

  @Property({ type: 'varchar', length: 50 })
  FirstName!: string;

  @Property({ type: 'varchar', length: 60 })
  LastName!: string;

  @Property({ type: 'varchar', length: 120, nullable: true })
  Email?: string;

  @Property({ type: 'varchar', length: 30, nullable: true })
  PhoneNumber?: string;

  @Property({ type: 'varchar', length: 100, default: '' })
  Password!: string;

  @Property({ type: 'int', unsigned: true, nullable: true })
  GroupID?: number;

  @Property({ type: 'int', unsigned: true, default: 3 })
  RoleID!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 1 })
  isActive!: number;

  @Property({ type: 'timestamp', nullable: true, onCreate: () => new Date() })
  created?: Date;

  @Property({ type: 'int', unsigned: true })
  TierID!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  Hidden!: number;

  @Property({ type: 'decimal', precision: 4, scale: 2, nullable: true })
  LaborRate?: number;

  @Property({ type: 'text', nullable: true })
  ProfilePhoto?: string;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  passChange!: number;

  @Property({ type: 'timestamp', nullable: true, onUpdate: () => new Date() })
  LastUpdated?: Date;

  @Property({ type: 'tinyint', unsigned: true, default: 0, nullable: true })
  deleted?: number;

  @Property({ type: 'varchar', length: 4, nullable: true })
  Pin?: string;

  @Property({ type: 'tinyint', unsigned: true })
  user_Shifts!: number;

  @Property({ type: 'int', unsigned: true })
  notificationFrequency!: number;

  @Property({ type: 'int', unsigned: true, default: 1 })
  ShiftType!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  float_mode!: number;

  @Property({ type: 'varchar', length: 50, default: '1' })
  zoho_contact_id!: string;

  // Relations
  @OneToMany(() => StageCode, stageCode => stageCode.createdByUser)
  createdStages = new Collection<StageCode>(this);

  @OneToMany(() => StageCode, stageCode => stageCode.updatedByUser)
  updatedStages = new Collection<StageCode>(this);

  @OneToMany(() => StageCode, stageCode => stageCode.deletedByUser)
  deletedStages = new Collection<StageCode>(this);

  @OneToMany(() => StageHistory, stageHistory => stageHistory.updatedByUser)
  stageHistory = new Collection<StageHistory>(this);
}
