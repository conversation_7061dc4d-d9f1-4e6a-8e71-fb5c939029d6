import { <PERSON>tity, <PERSON>Key, Property, ManyToOne, Enum, Index } from '@mikro-orm/core';
import { Stage, StageShape } from './Stage.entity';
import { User } from './User.entity';

@Entity({ tableName: 'stages_history' })
@Index({ name: 'idx_updated_by', properties: ['updated_by'] })
@Index({ name: 'idx_stage_id_active', properties: ['stage_id', 'active'] })
export class StageHistory {
  @PrimaryKey({ type: 'int', unsigned: true })
  id!: number;

  @Property({ type: 'int', unsigned: true })
  stage_id!: number;

  @Property({ type: 'varchar', length: 190 })
  name!: string;

  @Property({ type: 'int', unsigned: true })
  stage_work_level_id!: number;

  @Enum(() => StageShape)
  @Property({ type: 'enum', items: () => StageShape, default: StageShape.SQUARE })
  shape!: StageShape;

  @Property({ type: 'int', unsigned: true, nullable: true })
  stage_code_id?: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  metric_work_item_column_id?: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  locked!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  bim!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  fab!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  field!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  deleted!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  archived!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 1 })
  active!: number;

  @Property({ type: 'int', unsigned: true })
  updated_by!: number;

  @Property({ type: 'datetime', onCreate: () => new Date() })
  updated_on!: Date;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  nestable!: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  rejectable!: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  shipping_block_id?: number;

  @Property({ type: 'int', unsigned: true, nullable: true })
  shipping_block_position?: number;

  @Property({ type: 'tinyint', unsigned: true, default: 0 })
  groupable!: number;

  // Relations
  @ManyToOne(() => Stage, { joinColumn: 'stage_id' })
  stage!: Stage;

  @ManyToOne(() => User, { joinColumn: 'updated_by' })
  updatedByUser!: User;
}
