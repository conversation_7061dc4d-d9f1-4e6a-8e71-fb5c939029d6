import { EntityManager, Connection } from '@mikro-orm/core';
import { User } from './entities/User.entity';
import { Stage, StageShape } from './entities/Stage.entity';
import { WorkItemColumn } from './entities/WorkItemColumn.entity';
import { StageHistory } from './entities/StageHistory.entity';

export interface CreateStageParams {
  name: string;
  stage_work_level_id: number;
  shape?: string;
  stage_code_id?: number;
  metric_work_item_column_id?: number;
  locked: number;
  bim: number;
  fab: number;
  field: number;
  user_id: number;
  nestable: number;
  rejectable: number;
  groupable: number;
}

export interface CreateStageResult {
  status: 'success' | 'error';
  stage_id?: number;
  message: string;
}

export class MikroORMStageService {
  constructor(private em: EntityManager) {}

  // Method 1: Direct stored procedure call
  async createStageStoredProcedure(params: CreateStageParams): Promise<CreateStageResult> {
    try {
      const connection = this.em.getConnection();
      const result = await connection.execute(
        `CALL sp_create_stage(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          params.name,
          params.stage_work_level_id,
          params.shape || null,
          params.stage_code_id || null,
          params.metric_work_item_column_id || null,
          params.locked,
          params.bim,
          params.fab,
          params.field,
          params.user_id,
          params.nestable,
          params.rejectable,
          params.groupable
        ]
      );

      // The stored procedure returns a result set with status, stage_id, and message
      const spResult = result[0];
      
      return {
        status: spResult.status,
        stage_id: spResult.stage_id,
        message: spResult.message
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 2: Using MikroORM entities
  async createStageWithEntities(params: CreateStageParams): Promise<CreateStageResult> {
    const em = this.em.fork();
    
    try {
      return await em.transactional(async (em) => {
        // Step 1: Validate user exists and is active
        const userError = await this.validateUser(em, params.user_id);
        if (userError) {
          throw new Error(userError);
        }

        // Step 2: Validate stage name
        const nameError = this.validateStageName(params.name);
        if (nameError) {
          throw new Error(nameError);
        }

        // Step 3: Check for unique name
        const uniquenessError = await this.validateStageNameUniqueness(em, params.name);
        if (uniquenessError) {
          throw new Error(uniquenessError);
        }

        // Step 4: Validate stage_work_level_id is provided
        const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
        if (workLevelError) {
          throw new Error(workLevelError);
        }

        // Step 5: Check if metric column is custom (should not be allowed)
        const metricColumnId = await this.validateMetricColumn(em, params.metric_work_item_column_id);

        // Step 6: Create the stage
        const stage = new Stage();
        stage.name = params.name;
        stage.stage_work_level_id = params.stage_work_level_id;
        stage.shape = params.shape as StageShape;
        stage.stage_code_id = params.stage_code_id;
        stage.metric_work_item_column_id = metricColumnId;
        stage.locked = params.locked;
        stage.bim = params.bim;
        stage.fab = params.fab;
        stage.field = params.field;
        stage.nestable = params.nestable;
        stage.rejectable = params.rejectable;
        stage.groupable = params.groupable;

        em.persist(stage);
        await em.flush();

        if (!stage.id) {
          throw new Error('Failed to create stage');
        }

        // Step 7: Create history record
        await this.createStageHistoryRecord(em, stage.id, params.user_id);

        return {
          status: 'success' as const,
          stage_id: stage.id,
          message: 'Stage created'
        };
      });

    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Method 3: Using MikroORM Advanced Repository Methods (Query Builder-like)
  // This method uses MikroORM's advanced repository methods like count(), nativeUpdate(), etc.
  // Benefits: Programmatic query building, type safety, optimized operations
  async createStageWithQueryBuilder(params: CreateStageParams): Promise<CreateStageResult> {
    const em = this.em.fork();
    
    try {
      return await em.transactional(async (em) => {
        // Step 1: Validate user exists and is active using Query Builder
        const userError = await this.validateUserWithQueryBuilder(em, params.user_id);
        if (userError) {
          throw new Error(userError);
        }

        // Step 2: Validate stage name
        const nameError = this.validateStageName(params.name);
        if (nameError) {
          throw new Error(nameError);
        }

        // Step 3: Check for unique name using Query Builder
        const uniquenessError = await this.validateStageNameUniquenessWithQueryBuilder(em, params.name);
        if (uniquenessError) {
          throw new Error(uniquenessError);
        }

        // Step 4: Validate stage_work_level_id is provided
        const workLevelError = this.validateStageWorkLevelId(params.stage_work_level_id);
        if (workLevelError) {
          throw new Error(workLevelError);
        }

        // Step 5: Check if metric column is custom using Query Builder
        const metricColumnId = await this.validateMetricColumnWithQueryBuilder(em, params.metric_work_item_column_id);

        // Step 6: Create the stage using MikroORM's advanced repository methods
        const stage = new Stage();
        stage.name = params.name;
        stage.stage_work_level_id = params.stage_work_level_id;
        stage.shape = params.shape as StageShape;
        stage.stage_code_id = params.stage_code_id;
        stage.metric_work_item_column_id = metricColumnId;
        stage.locked = params.locked;
        stage.bim = params.bim;
        stage.fab = params.fab;
        stage.field = params.field;
        stage.nestable = params.nestable;
        stage.rejectable = params.rejectable;
        stage.groupable = params.groupable;

        em.persist(stage);
        await em.flush();

        const newStageId = stage.id;

        if (!newStageId) {
          throw new Error('Failed to create stage');
        }

        // Step 7: Create history record using Query Builder
        await this.createStageHistoryWithQueryBuilder(em, newStageId, params.user_id);

        return {
          status: 'success' as const,
          stage_id: newStageId,
          message: 'Stage created'
        };
      });

    } catch (error) {
      return {
        status: 'error',
        message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Private validation methods for MikroORM entities
  private async validateUser(em: EntityManager, userId: number): Promise<string | null> {
    const user = await em.findOne(User, {
      id: userId,
      deleted: 0,
      isActive: 1
    });

    return user ? null : 'User does not exist';
  }

  private validateStageName(name: string): string | null {
    if (!name || name.trim() === '') {
      return 'Name must be provided and must be unique';
    }
    return null;
  }

  private validateStageWorkLevelId(stageWorkLevelId: number): string | null {
    if (!stageWorkLevelId) {
      return 'stage_work_level_id is required';
    }
    return null;
  }

  private async validateStageNameUniqueness(em: EntityManager, name: string): Promise<string | null> {
    const existingStage = await em.findOne(Stage, {
      name: name,
      deleted: 0
    });

    return existingStage ? 'Name must be provided and must be unique' : null;
  }

  private async validateMetricColumn(em: EntityManager, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    const workItemColumn = await em.findOne(WorkItemColumn, {
      id: metricColumnId,
      is_custom: 1
    });

    return workItemColumn ? undefined : metricColumnId;
  }

  private async createStageHistoryRecord(em: EntityManager, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage
    await em.nativeUpdate(StageHistory,
      { stage_id: stageId, active: 1 },
      { active: 0 }
    );

    // Get the current stage data to create history record
    const stage = await em.findOne(Stage, { id: stageId });

    if (stage) {
      const stageHistory = new StageHistory();
      stageHistory.stage_id = stage.id;
      stageHistory.name = stage.name;
      stageHistory.stage_work_level_id = stage.stage_work_level_id;
      stageHistory.shape = stage.shape || StageShape.SQUARE;
      stageHistory.stage_code_id = stage.stage_code_id;
      stageHistory.metric_work_item_column_id = stage.metric_work_item_column_id;
      stageHistory.locked = stage.locked;
      stageHistory.bim = stage.bim;
      stageHistory.fab = stage.fab;
      stageHistory.field = stage.field;
      stageHistory.deleted = stage.deleted;
      stageHistory.archived = stage.archived;
      stageHistory.nestable = stage.nestable;
      stageHistory.rejectable = stage.rejectable;
      stageHistory.shipping_block_id = stage.shipping_block_id;
      stageHistory.shipping_block_position = stage.shipping_block_position;
      stageHistory.groupable = stage.groupable;
      stageHistory.active = 1;
      stageHistory.updated_by = userId;
      stageHistory.updated_on = new Date();

      em.persist(stageHistory);
      await em.flush();
    }
  }

  // Private validation methods for MikroORM Advanced Repository Methods (Query Builder-like)
  private async validateUserWithQueryBuilder(em: EntityManager, userId: number): Promise<string | null> {
    const userCount = await em.count(User, {
      id: userId,
      deleted: 0,
      isActive: 1
    });

    return userCount > 0 ? null : 'User does not exist';
  }

  private async validateStageNameUniquenessWithQueryBuilder(em: EntityManager, name: string): Promise<string | null> {
    const stageCount = await em.count(Stage, {
      name: name,
      deleted: 0
    });

    return stageCount > 0 ? 'Name must be provided and must be unique' : null;
  }

  private async validateMetricColumnWithQueryBuilder(em: EntityManager, metricColumnId?: number): Promise<number | undefined> {
    if (!metricColumnId) {
      return undefined;
    }

    const customColumn = await em.findOne(WorkItemColumn, {
      id: metricColumnId,
      is_custom: 1,
      deleted: 0
    });

    return customColumn ? undefined : metricColumnId;
  }

  private async createStageHistoryWithQueryBuilder(em: EntityManager, stageId: number, userId: number): Promise<void> {
    // First, deactivate any existing active history records for this stage using nativeUpdate
    await em.nativeUpdate(StageHistory,
      { stage_id: stageId, active: 1 },
      { active: 0 }
    );

    // Get the current stage data using findOne
    const stage = await em.findOne(Stage, { id: stageId });

    if (stage) {
      // Create new history record using MikroORM's advanced methods
      const stageHistory = new StageHistory();
      stageHistory.stage_id = stage.id;
      stageHistory.name = stage.name;
      stageHistory.stage_work_level_id = stage.stage_work_level_id;
      stageHistory.shape = stage.shape || StageShape.SQUARE;
      stageHistory.stage_code_id = stage.stage_code_id;
      stageHistory.metric_work_item_column_id = stage.metric_work_item_column_id;
      stageHistory.locked = stage.locked;
      stageHistory.bim = stage.bim;
      stageHistory.fab = stage.fab;
      stageHistory.field = stage.field;
      stageHistory.deleted = stage.deleted;
      stageHistory.archived = stage.archived;
      stageHistory.nestable = stage.nestable;
      stageHistory.rejectable = stage.rejectable;
      stageHistory.shipping_block_id = stage.shipping_block_id;
      stageHistory.shipping_block_position = stage.shipping_block_position;
      stageHistory.groupable = stage.groupable;
      stageHistory.active = 1;
      stageHistory.updated_by = userId;
      stageHistory.updated_on = new Date();

      em.persist(stageHistory);
      await em.flush();
    }
  }
}
