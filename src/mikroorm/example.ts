import { <PERSON>k<PERSON><PERSON><PERSON>, EntityManager } from '@mikro-orm/core';
import { MySqlDriver } from '@mikro-orm/mysql';
import { MikroORMStageBenchmark } from './StageBenchmark';
import { 
  User, 
  Stage, 
  StageWorkLevel, 
  StageCode, 
  WorkItemColumn, 
  StageHistory 
} from './index';

// Example usage of the MikroORM Stage benchmark
async function runMikroORMStageBenchmarkExample() {
  // Create MikroORM instance
  const orm = await MikroORM.init<MySqlDriver>({
    driver: MySqlDriver,
    host: 'localhost',
    port: 3306,
    user: 'your_username',
    password: 'your_password',
    dbName: 'accoc_staging',
    entities: [User, Stage, StageWorkLevel, StageCode, WorkItemColumn, StageHistory],
    debug: false, // Set to true for SQL query logging
    allowGlobalContext: true // For this example only
  });

  try {
    // Get EntityManager
    const em = orm.em.fork();
    console.log('MikroORM database connection established');

    // Create benchmark instance
    const benchmark = new MikroORMStageBenchmark(em);

    // Run a single benchmark test
    console.log('\n=== Running Single MikroORM Benchmark Test ===');
    const singleResult = await benchmark.runSingleBenchmark({
      name: `MikroORM_Test_Stage_${Date.now()}`,
      stage_work_level_id: 1,
      shape: 'Square',
      stage_code_id: 1,
      metric_work_item_column_id: 1,
      locked: 0,
      bim: 1,
      fab: 1,
      field: 0,
      user_id: 1,
      nestable: 1,
      rejectable: 1,
      groupable: 1
    });

    console.log('Single test results:');
    console.log(`Stored Procedure:  ${singleResult.storedProcedure.executionTime.toFixed(2)}ms - ${singleResult.storedProcedure.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`MikroORM Entities: ${singleResult.entities.executionTime.toFixed(2)}ms - ${singleResult.entities.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`Query Builder:     ${singleResult.queryBuilder.executionTime.toFixed(2)}ms - ${singleResult.queryBuilder.success ? 'SUCCESS' : 'FAILED'}`);

    // Run full benchmark suite
    console.log('\n=== Running Full MikroORM Benchmark Suite ===');
    const results = await benchmark.runBenchmarkSuite(10); // 10 iterations

    // Print formatted results
    benchmark.printResults(results);

    // You can also access raw data for further analysis
    console.log('\n=== Raw Data Sample ===');
    console.log('First 3 stored procedure results:');
    results.storedProcedure.slice(0, 3).forEach((result, index) => {
      console.log(`  ${index + 1}: ${result.executionTime.toFixed(2)}ms - ${result.success ? 'SUCCESS' : 'FAILED'}`);
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
    });

    // Clean up test data
    console.log('\n=== Cleaning Up Test Data ===');
    await benchmark.cleanupTestData();

    // Example of direct MikroORM operations for comparison
    console.log('\n=== Direct MikroORM Operations Example ===');
    
    // Create a stage using MikroORM entities directly
    const directStage = new Stage();
    directStage.name = `Direct_MikroORM_Stage_${Date.now()}`;
    directStage.stage_work_level_id = 1;
    directStage.shape = 'Diamond' as any;
    directStage.locked = 0;
    directStage.bim = 1;
    directStage.fab = 1;
    directStage.field = 0;
    directStage.nestable = 1;
    directStage.rejectable = 1;
    directStage.groupable = 1;

    em.persist(directStage);
    await em.flush();

    console.log('Direct MikroORM stage created:', {
      id: directStage.id,
      name: directStage.name
    });

    // Query stages with relations
    const stagesWithRelations = await em.find(Stage, 
      { 
        deleted: 0,
        name: { $like: '%MikroORM%' }
      }, 
      { 
        populate: ['stageWorkLevel', 'stageCode', 'history'],
        limit: 5
      }
    );

    console.log(`Found ${stagesWithRelations.length} stages with relations`);

    // Example of Raw SQL query
    const stageCount = await em.getConnection().execute(
      'SELECT COUNT(*) as count FROM stages WHERE deleted = 0'
    );
    console.log(`Total active stages (Raw SQL): ${stageCount[0].count}`);

    // Clean up the direct example stage
    em.remove(directStage);
    await em.flush();

  } catch (error) {
    console.error('MikroORM benchmark failed:', error);
  } finally {
    // Clean up
    await orm.close();
    console.log('\nMikroORM database connection closed');
  }
}

// Export for use in other files
export { runMikroORMStageBenchmarkExample };

// Uncomment to run directly
// runMikroORMStageBenchmarkExample().catch(console.error);
